/**
 * 小红书配置设置界面
 */

"ui";  // 声明为UI模式

// 直接使用内置的配置管理
var configManager = 创建简单配置管理();
console.log("✅ 使用内置配置管理");

// 当前配置
var 当前配置 = {};

/**
 * 创建简单的配置管理（AutoJS版本）
 */
function 创建简单配置管理() {
    // 固定配置文件路径到手机根目录
    var CONFIG_FILE = "/sdcard/xiaohongshu_config.json";

    var DEFAULT_CONFIG = {
        操作换号几次改机: 4,
        最大账号处理数量: 10,
        当前账号处理进度: 0,
        链接文件路径: "/sdcard/xiaohongshu_url.txt",
        当前操作行号: 1,
        启用点赞: true,
        启用收藏: true,
        启用阅读: true,
        每个账号点赞数量: 3,
        每个账号收藏数量: 3,
        每个账号阅读数量: 3,
        阅读时间: 10000,
        跳转链接延时: 3000,

        // 新增功能开关
        启用飞行模式: false,
        启用备份还原: false,
        启用小红书操作: true,
        启用改机重启: false,
        换号从头开始: true
    };

    return {
        CONFIG_FILE: CONFIG_FILE,
        DEFAULT_CONFIG: DEFAULT_CONFIG,

        读取配置: function() {
            try {
                // 如果配置文件不存在，创建默认配置
                if (!files.exists(CONFIG_FILE)) {
                    this.保存配置(DEFAULT_CONFIG);
                    return DEFAULT_CONFIG;
                }

                // 读取配置文件
                var 配置内容 = files.read(CONFIG_FILE);
                if (!配置内容) {
                    return DEFAULT_CONFIG;
                }

                var 配置对象 = JSON.parse(配置内容);
                // 合并默认配置
                return Object.assign({}, DEFAULT_CONFIG, 配置对象);

            } catch (e) {
                console.error("读取配置失败: " + e.message);
                return DEFAULT_CONFIG;
            }
        },

        保存配置: function(配置对象) {
            try {
                var 配置JSON = JSON.stringify(配置对象, null, 2);
                files.write(CONFIG_FILE, 配置JSON);
                console.log("配置已保存到: " + CONFIG_FILE);
                return true;
            } catch (e) {
                console.error("保存配置失败: " + e.message);
                return false;
            }
        },

        获取配置项: function(键名, 默认值) {
            var 配置 = this.读取配置();
            return 配置.hasOwnProperty(键名) ? 配置[键名] : 默认值;
        },

        设置配置项: function(键名, 值) {
            var 配置 = this.读取配置();
            配置[键名] = 值;
            return this.保存配置(配置);
        }
    };
}

/**
 * 使用ui.layout方式创建界面
 */
function 显示主界面() {
    try {
        // 读取当前配置
        当前配置 = configManager.读取配置();

        // 检查配置是否为空
        if (!当前配置 || typeof 当前配置 !== 'object') {
            console.error("配置读取失败，使用默认配置");
            当前配置 = configManager.DEFAULT_CONFIG;
        }

        console.log("当前配置:", 当前配置);

        // 使用ui.layout创建界面 - 添加滚动视图和紧凑布局
        ui.layout(
            <vertical>
                <text text="小红书自动操作设置" textSize="18sp" textColor="#333" gravity="center" margin="8 8 8 8"/>

                <ScrollView layout_weight="1">
                    <vertical padding="12">

                        <card cardCornerRadius="6dp" cardElevation="2dp" margin="0 0 6 0">
                            <vertical padding="12">
                                <text text="小红书操作开关" textSize="14sp" textColor="#666" textStyle="bold" margin="0 0 0 6"/>
                                <horizontal margin="0 2 0 0">
                                    <checkbox id="checkbox_like" text="启用点赞" textSize="13sp" w="120dp" singleLine="true"/>
                                    <checkbox id="checkbox_collect" text="启用收藏" textSize="13sp" w="120dp" singleLine="true"/>
                                </horizontal>
                                <horizontal margin="0 8 0 0">
                                    <checkbox id="checkbox_read" text="启用阅读" textSize="13sp" w="120dp" singleLine="true"/>
                                    <View layout_weight="1"/>
                                </horizontal>
                            </vertical>
                        </card>

                        <card cardCornerRadius="6dp" cardElevation="2dp" margin="0 0 6 0">
                            <vertical padding="12">
                                <text text="系统功能开关" textSize="14sp" textColor="#666" textStyle="bold" margin="0 0 0 6"/>
                                <horizontal margin="0 2 0 0">
                                    <checkbox id="checkbox_airplane" text="飞行模式" textSize="13sp" w="120dp" singleLine="true"/>
                                    <checkbox id="checkbox_backup" text="备份还原" textSize="13sp" w="120dp" singleLine="true"/>
                                </horizontal>
                                <horizontal margin="0 8 0 0">
                                    <checkbox id="checkbox_xiaohongshu" text="小红书操作" textSize="13sp" w="140dp" singleLine="true"/>
                                    <checkbox id="checkbox_reboot" text="改机重启" textSize="13sp" w="120dp" singleLine="true"/>
                                </horizontal>
                            </vertical>
                        </card>

                        <card cardCornerRadius="6dp" cardElevation="2dp" margin="0 0 6 0">
                            <vertical padding="12">
                                <text text="操作数量" textSize="14sp" textColor="#666" textStyle="bold" margin="0 0 0 6"/>
                                <horizontal margin="0 4 0 0">
                                    <text text="点赞数量" layout_weight="1" textSize="14sp"/>
                                    <input id="input_like_count" inputType="number" w="60dp" textSize="14sp"/>
                                </horizontal>
                                <horizontal margin="0 4 0 0">
                                    <text text="收藏数量" layout_weight="1" textSize="14sp"/>
                                    <input id="input_collect_count" inputType="number" w="60dp" textSize="14sp"/>
                                </horizontal>
                                <horizontal margin="0 4 0 0">
                                    <text text="阅读数量" layout_weight="1" textSize="14sp"/>
                                    <input id="input_read_count" inputType="number" w="60dp" textSize="14sp"/>
                                </horizontal>
                            </vertical>
                        </card>

                        <card cardCornerRadius="6dp" cardElevation="2dp" margin="0 0 6 0">
                            <vertical padding="12">
                                <text text="时间控制" textSize="14sp" textColor="#666" textStyle="bold" margin="0 0 0 6"/>
                                <horizontal margin="0 4 0 0">
                                    <text text="阅读时间(秒)" layout_weight="1" textSize="14sp"/>
                                    <input id="input_read_time" inputType="number" w="80dp" textSize="14sp"/>
                                </horizontal>
                                <horizontal margin="0 4 0 0">
                                    <text text="跳转延时(秒)" layout_weight="1" textSize="14sp"/>
                                    <input id="input_link_delay" inputType="number" w="80dp" textSize="14sp"/>
                                </horizontal>
                            </vertical>
                        </card>

                        <card cardCornerRadius="6dp" cardElevation="2dp" margin="0 0 6 0">
                            <vertical padding="12">
                                <text text="核心配置" textSize="14sp" textColor="#666" textStyle="bold" margin="0 0 0 6"/>
                                <horizontal margin="0 4 0 0">
                                    <text text="操作换号几次改机" layout_weight="1" textSize="14sp"/>
                                    <input id="input_change_count" inputType="number" w="60dp" textSize="14sp"/>
                                </horizontal>
                                <horizontal margin="0 4 0 0">
                                    <text text="最大账号处理数量" layout_weight="1" textSize="14sp"/>
                                    <input id="input_max_accounts" inputType="number" w="60dp" textSize="14sp"/>
                                </horizontal>
                                <horizontal margin="0 4 0 0">
                                    <text text="链接文件" layout_weight="1" textSize="14sp"/>
                                    <input id="input_url_file" w="150dp" textSize="12sp"/>
                                </horizontal>
                            </vertical>
                        </card>

                        <card cardCornerRadius="6dp" cardElevation="2dp" margin="0 0 6 0">
                            <vertical padding="12">
                                <text text="进度控制" textSize="14sp" textColor="#666" textStyle="bold" margin="0 0 0 6"/>
                                <horizontal margin="0 4 0 0">
                                    <text text="当前操作行号" layout_weight="1" textSize="14sp"/>
                                    <input id="input_current_line" inputType="number" w="80dp" textSize="14sp"/>
                                    <text id="text_total_lines" text="/0" textSize="14sp" textColor="#999" margin="4 0 0 0"/>
                                </horizontal>
                                <horizontal margin="0 4 0 0">
                                    <text text="当前账号进度" layout_weight="1" textSize="14sp"/>
                                    <input id="input_current_account" inputType="number" w="80dp" textSize="14sp"/>
                                    <text id="text_max_accounts" text="/10" textSize="14sp" textColor="#999" margin="4 0 0 0"/>
                                </horizontal>
                                <horizontal margin="0 4 0 0">
                                    <button id="btn_reset_line" text="重置行号" w="80dp" textSize="12sp" margin="0 2 0 0"/>
                                    <button id="btn_reset_account" text="重置账号" w="80dp" textSize="12sp" margin="0 2 0 0"/>
                                    <button id="btn_refresh_progress" text="刷新进度" w="80dp" textSize="12sp" margin="0 0 0 2"/>
                                </horizontal>
                                <text id="text_progress_info" text="提示：行号控制链接进度，账号进度控制换号次数" textSize="12sp" textColor="#999" margin="0 4 0 0"/>
                            </vertical>
                        </card>

                        <card cardCornerRadius="6dp" cardElevation="2dp" margin="0 0 6 0">
                            <vertical padding="12">
                                <text text="其他设置" textSize="14sp" textColor="#666" textStyle="bold" margin="0 0 0 6"/>
                                <horizontal margin="0 4 0 0">
                                    <checkbox id="checkbox_restart_from_beginning" text="换号从头开始" textSize="13sp" w="140dp" singleLine="true"/>
                                    <View layout_weight="1"/>
                                </horizontal>
                                <text text="提示：启用后每次换号时会重置链接进度从第1行开始" textSize="12sp" textColor="#999" margin="0 4 0 0"/>
                            </vertical>
                        </card>

                    </vertical>
                </ScrollView>

                <horizontal margin="8 8 8 8">
                    <button id="btn_save" text="保存" layout_weight="1" margin="0 4 0 0" textSize="14sp"/>
                    <button id="btn_close" text="关闭" layout_weight="1" margin="0 0 0 4" textSize="14sp"/>
                </horizontal>

                <text id="status_text" text="就绪" textColor="#666" gravity="center" margin="4 4 4 8" textSize="12sp"/>
            </vertical>
        );

        // 设置初始值
        设置界面初始值();

        // 绑定事件
        绑定界面事件();

    } catch (e) {
        console.error("显示主界面出错: " + e.message);
        toast("界面显示失败: " + e.message);
    }
}

/**
 * 设置界面初始值
 */
function 设置界面初始值() {
    try {
        // 设置小红书操作开关状态
        ui.checkbox_like.checked = 当前配置.启用点赞 || false;
        ui.checkbox_collect.checked = 当前配置.启用收藏 || false;
        ui.checkbox_read.checked = 当前配置.启用阅读 || false;

        // 设置系统功能开关状态
        ui.checkbox_airplane.checked = 当前配置.启用飞行模式 || false;
        ui.checkbox_backup.checked = 当前配置.启用备份还原 || false;
        ui.checkbox_xiaohongshu.checked = 当前配置.启用小红书操作 !== false; // 默认为true
        ui.checkbox_reboot.checked = 当前配置.启用改机重启 || false;
        ui.checkbox_restart_from_beginning.checked = 当前配置.换号从头开始 !== false; // 默认为true

        // 设置输入框值 - 时间转换为秒显示
        ui.input_like_count.setText(String(当前配置.每个账号点赞数量 || 3));
        ui.input_collect_count.setText(String(当前配置.每个账号收藏数量 || 3));
        ui.input_read_count.setText(String(当前配置.每个账号阅读数量 || 3));
        ui.input_read_time.setText(String((当前配置.阅读时间 || 10000) / 1000)); // 毫秒转秒
        ui.input_link_delay.setText(String((当前配置.跳转链接延时 || 3000) / 1000)); // 毫秒转秒
        ui.input_change_count.setText(String(当前配置.操作换号几次改机 || 4));
        ui.input_max_accounts.setText(String(当前配置.最大账号处理数量 || 10));
        ui.input_url_file.setText(当前配置.链接文件路径 || "/sdcard/xiaohongshu_url.txt");

        // 设置当前操作行号和账号进度
        ui.input_current_line.setText(String(当前配置.当前操作行号 || 1));
        ui.input_current_account.setText(String(当前配置.当前账号处理进度 || 0));

        // 更新最大账号数量显示
        ui.text_max_accounts.setText("/" + (当前配置.最大账号处理数量 || 10));

        // 更新进度信息
        更新进度信息();

        console.log("界面初始值设置完成");
    } catch (e) {
        console.error("设置界面初始值出错: " + e.message);
    }
}

/**
 * 绑定界面事件
 */
function 绑定界面事件() {
    // 保存配置
    ui.btn_save.click(function() {
        保存当前配置();
    });

    // 关闭按钮
    ui.btn_close.click(function() {
        var confirmed = dialogs.confirm("确认关闭", "是否关闭配置界面？");
        if (confirmed) {
            ui.finish();
        }
    });

    // 重置行号按钮
    ui.btn_reset_line.click(function() {
        ui.input_current_line.setText("1");
        toast("已重置为第1行");
    });

    // 重置账号进度按钮
    ui.btn_reset_account.click(function() {
        ui.input_current_account.setText("0");
        toast("已重置账号进度为0");
    });

    // 刷新进度按钮
    ui.btn_refresh_progress.click(function() {
        更新进度信息();
        toast("进度信息已刷新");
    });


}

/**
 * 保存当前配置
 */
function 保存当前配置() {
    try {
        // 读取界面值 - 使用Object.assign代替展开运算符
        var 新配置 = Object.assign({}, 当前配置, {
            // 小红书操作开关
            启用点赞: ui.checkbox_like.checked,
            启用收藏: ui.checkbox_collect.checked,
            启用阅读: ui.checkbox_read.checked,

            // 系统功能开关
            启用飞行模式: ui.checkbox_airplane.checked,
            启用备份还原: ui.checkbox_backup.checked,
            启用小红书操作: ui.checkbox_xiaohongshu.checked,
            启用改机重启: ui.checkbox_reboot.checked,
            换号从头开始: ui.checkbox_restart_from_beginning.checked,

            // 数量和时间配置
            每个账号点赞数量: parseInt(ui.input_like_count.text()) || 0,
            每个账号收藏数量: parseInt(ui.input_collect_count.text()) || 0,
            每个账号阅读数量: parseInt(ui.input_read_count.text()) || 0,
            阅读时间: (parseInt(ui.input_read_time.text()) || 10) * 1000, // 秒转毫秒
            跳转链接延时: (parseInt(ui.input_link_delay.text()) || 3) * 1000, // 秒转毫秒
            操作换号几次改机: parseInt(ui.input_change_count.text()) || 4,
            最大账号处理数量: parseInt(ui.input_max_accounts.text()) || 10,
            当前账号处理进度: parseInt(ui.input_current_account.text()) || 0,
            链接文件路径: ui.input_url_file.text() || "/sdcard/xiaohongshu_url.txt",
            当前操作行号: parseInt(ui.input_current_line.text()) || 1
        });

        // 检查并创建URL文件
        检查并创建URL文件(新配置.链接文件路径);

        // 保存配置
        if (configManager.保存配置(新配置)) {
            ui.status_text.setText("配置保存成功");
            toast("保存成功");
            当前配置 = 新配置;
        } else {
            ui.status_text.setText("配置保存失败");
            toast("保存失败");
        }

    } catch (e) {
        console.error("保存配置出错: " + e.message);
        ui.status_text.setText("保存出错: " + e.message);
        toast("保存出错");
    }
}

/**
 * 更新进度信息
 */
function 更新进度信息() {
    try {
        // 读取URL文件获取总行数
        var URL文件路径 = 当前配置.链接文件路径 || "/sdcard/xiaohongshu_url.txt";
        var 总行数 = 0;

        if (files.exists(URL文件路径)) {
            var URL内容 = files.read(URL文件路径);
            if (URL内容) {
                var URL行列表 = URL内容.split('\n').filter(function(行) {
                    return 行.trim().length > 0;
                });
                总行数 = URL行列表.length;
            }
        }

        // 更新总行数显示
        ui.text_total_lines.setText("/" + 总行数);

        // 更新进度提示信息
        var 当前行号 = parseInt(ui.input_current_line.text()) || 1;
        var 当前账号进度 = parseInt(ui.input_current_account.text()) || 0;
        var 最大账号数量 = parseInt(ui.input_max_accounts.text()) || 10;
        var 进度信息 = "";

        if (总行数 === 0) {
            进度信息 = "⚠️ URL文件不存在或为空，请先添加链接";
        } else if (当前行号 > 总行数) {
            进度信息 = "⚠️ 当前行号超出范围，将从第1行重新开始";
        } else {
            var 链接进度百分比 = Math.round((当前行号 / 总行数) * 100);
            var 账号进度百分比 = Math.round((当前账号进度 / 最大账号数量) * 100);
            进度信息 = `📍 链接进度: ${链接进度百分比}% (${当前行号}/${总行数}) | 账号进度: ${账号进度百分比}% (${当前账号进度}/${最大账号数量})`;
        }

        ui.text_progress_info.setText(进度信息);

    } catch (e) {
        console.error("更新进度信息出错: " + e.message);
        ui.text_progress_info.setText("❌ 进度信息更新失败");
    }
}

/**
 * 检查并创建URL文件
 */
function 检查并创建URL文件(文件路径) {
    try {
        if (!files.exists(文件路径)) {
            console.log("URL文件不存在，创建空文件: " + 文件路径);

            // 创建默认的URL文件内容
            var 默认内容 = "# 小红书链接文件\n" +
                         "# 请在下面添加小红书链接，每行一个\n" +
                         "# 支持的格式:\n" +
                         "# - https://www.xiaohongshu.com/explore/...\n" +
                         "# - https://xhslink.com/...\n" +
                         "# \n" +
                         "# 示例:\n" +
                         "# https://www.xiaohongshu.com/explore/123456789\n" +
                         "# https://xhslink.com/abcdef\n" +
                         "\n";

            files.write(文件路径, 默认内容);
            console.log("✅ 已创建URL文件: " + 文件路径);
            toast("已创建URL文件");
        } else {
            console.log("URL文件已存在: " + 文件路径);
        }
    } catch (e) {
        console.error("创建URL文件失败: " + e.message);
        toast("创建URL文件失败: " + e.message);
    }
}

// 主程序入口
function main() {
    console.log("启动小红书配置设置界面");
    
    // 检查权限
    if (!files.exists("/sdcard/")) {
        toast("需要存储权限");
        return;
    }
    
    // 显示主界面
    显示主界面();
}

// AutoJS中直接启动主程序
main();

// AutoJS中不需要module.exports
// 直接调用main函数启动程序
