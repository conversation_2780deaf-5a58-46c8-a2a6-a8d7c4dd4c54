/**
 * 小红书自动点赞脚本 - 主程序
 * 作者: Claude
 * 日期: 2025-07-01
 */

// 引入依赖
// 在AutoJS环境中，使用全局变量而不是require
var DeviceOperation, ErrorHandler, OCR, flows, 小红书操作, Process;

/**
 * 检查截图权限
 * @returns {boolean} 是否有截图权限
 */
function 检查截图权限() {
    console.log("检查截图权限...");
    try {
        // 如果OCR模块已加载并有该函数，则使用OCR模块的函数
        if (typeof OCR !== 'undefined' && OCR && typeof OCR.检查截图权限 === 'function') {
            console.log("使用OCR模块的检查截图权限函数");
            return OCR.检查截图权限();
        }
        
        // 否则使用简单的截图测试方法
        console.log("使用简单截图测试方法");
        let testImg = captureScreen();
        if (testImg) {
            console.log("截图测试成功");
            try {
                testImg.recycle();
            } catch (e) {
                // 忽略recycle错误
            }
            return true;
        }
        console.log("截图测试失败");
        return false;
    } catch (e) {
        console.error("检查截图权限出错: " + e.message);
        return false;
    }
}

// 将函数导出到全局作用域，确保其他模块可以使用
if (typeof global !== 'undefined') {
    global.检查截图权限 = 检查截图权限;
}

// 如果有module.exports，也导出到模块
if (typeof module !== 'undefined' && module.exports) {
    if (!module.exports) module.exports = {};
    module.exports.检查截图权限 = 检查截图权限;
}

// 在AutoJS环境中使用require加载模块
try {
    console.log("在AutoJS环境中加载模块...");

    // 先加载DeviceOperation模块，因为其他模块可能依赖它的操作模式定义
    DeviceOperation = require('./DeviceOperation.js');
    console.log("DeviceOperation.js 加载完成");

    // 使用require加载模块
    var hid = require('./hid.js');
    console.log("hid.js 加载完成");

    // 读取 config.txt 配置（如果存在）
    var 启动配置 = {};
    try {
        var configPath = files.path("./config.txt");
        if (files.exists(configPath)) {
            var configLines = files.read(configPath).split(/\r?\n/);
            for (var i = 0; i < configLines.length; i++) {
                var line = configLines[i].trim();
                if (!line || line.startsWith("#")) continue;
                var parts = line.split("=");
                if (parts.length === 2) {
                    var key = parts[0].trim();
                    var value = parts[1].trim();
                    启动配置[key] = value;
                }
            }
            console.log("已读取config.txt配置: " + JSON.stringify(启动配置));
        } else {
            console.log("未找到config.txt，使用默认配置");
        }
    } catch (e) {
        console.error("读取config.txt出错: " + e.message);
    }

    // 加载OCR模块
    OCR = require('./ocr.js');
    console.log("OCR.js 加载完成");

    // 修改模块加载方式，确保正确引用
    try {
        小红书操作 = require('./xiaohongshu.js');
        console.log("小红书操作.js 加载完成");
        // 如果有读取到配置，设置到小红书API模块
        if (typeof 小红书操作 !== 'undefined' && typeof 小红书操作.设置API配置 === 'function') {
            小红书操作.设置API配置(
                启动配置["基本URL"] || undefined,
                启动配置["用户名"] || undefined,
                undefined, // 设备令牌
                undefined  // 设备名称
            );
        }
        // 确保全局有 开始小红书点赞操作
        if (小红书操作 && typeof 小红书操作.开始小红书点赞操作 === 'function') {
            global.开始小红书点赞操作 = 小红书操作.开始小红书点赞操作;
            console.log("已将 开始小红书点赞操作 赋值到全局");
        } else {
            console.error("xiaohongshu.js 未正确导出 开始小红书点赞操作");
        }
        
        // 检查模块是否包含必要的函数
        if (typeof 小红书操作.获取配置项 !== 'function') {
            console.log("小红书操作模块中没有获取配置项函数，尝试其他方法获取");
            // 检查是否有其他可能的函数名
            if (typeof 小红书操作.getConfig === 'function') {
                console.log("找到getConfig函数，创建兼容函数");
                小红书操作.获取配置项 = 小红书操作.getConfig;
            } else if (typeof 小红书操作.获取设置 === 'function') {
                console.log("找到获取设置函数，创建兼容函数");
                小红书操作.获取配置项 = 小红书操作.获取设置;
            }
        }
    } catch (e) {
        console.error("加载小红书操作模块出错: " + e);
        // 创建一个空的小红书操作模块，避免后续出错
        小红书操作 = {
            获取配置项: function(名称) {
                console.log("使用默认配置项: " + 名称);
                return 名称 === "操作几次恢复出厂设置" ? 4 : null;
            }
        };
    }

    ErrorHandler = require('./ErrorHandler.js');
    console.log("ErrorHandler.js 加载完成");

    // 加载flows模块
    var flows模块 = require('./flows.js');
    console.log("flows.js 加载完成");

    // 确保flows变量可用
    if (typeof flows === 'undefined') {
        if (typeof flows模块 === 'object') {
            flows = flows模块;
            console.log("从flows模块导入flows对象");
        } else if (typeof global !== 'undefined' && global.flows) {
            flows = global.flows;
            console.log("从全局变量获取flows对象");
        } else {
            console.error("无法获取flows对象");
        }
    }

    Process = require('./flows.js'); // 导入Process模块

    var { 备份还原, 删除第一个备份, 恢复出厂设置, 重启飞行模式 } = require('./flows.js');
    var { 开始小红书点赞操作 } = require('./xiaohongshu.js');

    console.log("所有模块加载成功");
} catch (e) {
    console.error("模块加载出错: " + e);
    exit();
}

/**
 * 全局配置参数
 */
var 全局配置 = {
    // 运行参数
    最大运行时间: 3600000,  // 最大运行时间(毫秒)，默认1小时
    默认等待时间: 2000,     // 默认等待时间(毫秒)
    调试模式: false,       // 是否开启调试模式

    // 错误处理
    最大连续错误: 5,       // 最大连续错误次数
    错误恢复策略: "返回",   // 错误恢复策略: 返回, 返回到桌面, 重启应用
    最大错误记录数: 100,    // 最大错误记录数
    最低电量: 5,           // 最低电量百分比
};

// 添加重试计数器
var 加载重试次数 = 0;
var 最大重试次数 = 10;

/**
 * 检查是否有ROOT权限
 * @returns {boolean} - 是否有ROOT权限
 */
function 检查ROOT权限() {
    console.log("检查ROOT权限...");
    try {
        // 方法1: 使用shell命令执行su -c 'id'
        let result = shell("su -c 'id'", true);
        if (result.code === 0 && result.result && result.result.indexOf("uid=0") !== -1) {
            console.log("检测到ROOT权限(方法1)");
            return true;
        }
        
        // 方法2: 尝试写入system目录
        result = shell("su -c 'touch /system/test_root && rm /system/test_root'", true);
        if (result.code === 0) {
            console.log("检测到ROOT权限(方法2)");
            return true;
        }
        
        // 方法3: 尝试执行简单ROOT命令
        try {
            let cmd = "su -c 'echo success'";
            let output = shell(cmd, true).result.trim();
            if (output === "success") {
                console.log("检测到ROOT权限(方法3)");
                return true;
            }
        } catch (e) {
            console.log("方法3检测ROOT失败: " + e.message);
        }
        
        // 方法4: 如果DeviceOperation模块已加载，使用其方法检测
        if (typeof DeviceOperation !== 'undefined' && typeof DeviceOperation.检查ROOT权限 === 'function') {
            let hasRoot = DeviceOperation.检查ROOT权限();
            if (hasRoot) {
                console.log("检测到ROOT权限(方法4)");
                return true;
            }
        }
        
        console.log("未检测到ROOT权限");
        return false;
    } catch (e) {
        console.error("检查ROOT权限出错: " + e.message);
        return false;
    }
}

/**
 * 使用ROOT权限开启截图权限
 * @returns {boolean} - 是否成功开启
 */
function 使用ROOT开启截图权限() {
    console.log("使用ROOT权限开启截图权限...");
    try {
        // 首次尝试获取root权限
        console.log("首次尝试获取root权限...");
        let result = shell("su -c 'echo success'", true);
        if (result.code === 0 && result.result.trim() === "success") {
            console.log("成功获取root权限，标记为已授权");
        } else {
            console.log("首次ROOT授权可能失败，继续尝试其他方法");
        }
        
        // 方法1: 尝试模拟用户点击授权按钮 - 多个位置尝试点击
        let 点击坐标集 = [
            [900, 1200],  // 中下部常见授权按钮位置
            [950, 1600],  // 底部常见授权按钮位置
            [550, 1700],  // 底部中间可能的授权按钮
            [800, 1900]   // 底部右侧常见授权按钮位置
        ];
        
        for (let 坐标 of 点击坐标集) {
            console.log(`尝试点击可能的授权按钮位置: (${坐标[0]}, ${坐标[1]})`);
            shell(`su -c 'input tap ${坐标[0]} ${坐标[1]}'`, true);
            sleep(500);  // 短暂等待响应
        }
        
        // 方法2: 使用ADB命令直接授权(适用于部分设备)
        shell("su -c 'settings put secure android_id 1'", true);
        
        // 方法3: 通过媒体投影权限授权(适用于部分设备)
        shell("su -c 'settings put global hidden_api_policy 1'", true);
        shell("su -c 'settings put global hidden_api_policy_pre_p_apps 1'", true);
        shell("su -c 'settings put global hidden_api_policy_p_apps 1'", true);
        
        // 等待权限生效
        sleep(1500);
        
        // 检查是否已获取权限
        let 权限检查次数 = 0;
        let 最大检查次数 = 3;
        let 已获取权限 = false;
        
        while (权限检查次数 < 最大检查次数 && !已获取权限) {
            权限检查次数++;
            console.log(`第${权限检查次数}次检查截图权限...`);
            
            if (typeof OCR !== 'undefined' && typeof OCR.检查截图权限 === 'function') {
                已获取权限 = OCR.检查截图权限();
                if (已获取权限) {
                    console.log("使用ROOT成功开启截图权限");
                    return true;
                }
            } else {
                // 如果没有检查函数，尝试直接创建截图
                try {
                    let img = captureScreen();
                    if (img) {
                        console.log("成功创建截图，说明已获取权限");
                        已获取权限 = true;
                        return true;
                    }
                } catch (e) {
                    console.log("尝试创建截图失败: " + e.message);
                }
            }
            
            if (!已获取权限 && 权限检查次数 < 最大检查次数) {
                console.log("截图权限检查失败，继续尝试点击授权");
                // 再次尝试点击不同位置
                let x = 800 + Math.floor(Math.random() * 200);
                let y = 1500 + Math.floor(Math.random() * 400);
                shell(`su -c 'input tap ${x} ${y}'`, true);
                sleep(1000);
            }
        }
        
        console.log("使用ROOT开启截图权限失败");
        return false;
    } catch (e) {
        console.error("使用ROOT开启截图权限出错: " + e.message);
        return false;
    }
}

/**
 * 主函数
 */
function main() {
    console.log("======== 小红书自动点赞脚本启动 ========");
    if (typeof device !== 'undefined') {
        console.log("操作系统: " + device.brand + " " + device.model + ", Android " + device.release);
        console.log("屏幕分辨率: " + device.width + "x" + device.height);
    }

    // 检查模块加载状态
    console.log("检查模块加载状态：");
    console.log("DeviceOperation: " + (typeof DeviceOperation !== 'undefined' ? "已加载" : "未加载"));
    console.log("OCR: " + (typeof OCR !== 'undefined' ? "已加载" : "未加载"));
    console.log("ErrorHandler: " + (typeof ErrorHandler !== 'undefined' ? "已加载" : "未加载"));
    console.log("flows: " + (typeof flows !== 'undefined' ? "已加载" : "未加载"));
    
    // 检查ROOT权限
    let 有ROOT权限 = 检查ROOT权限();
    console.log("ROOT权限检查结果: " + (有ROOT权限 ? "已获取" : "未获取"));
    
    // 确保无障碍服务和截图权限
    if (typeof DeviceOperation !== 'undefined') {
        // 确保无障碍服务（DeviceOperation.js中会根据当前模式判断是否需要）
        DeviceOperation.确保无障碍服务();
        
        // 申请截图权限 - 增强版，优先使用ROOT权限开启
        try {
            console.log("开始申请截图权限...");
            let 已有权限 = false;
            let 最大尝试次数 = 5;
            let 当前尝试次数 = 0;
            
            while (!已有权限 && 当前尝试次数 < 最大尝试次数) {
                当前尝试次数++;
                console.log(`第 ${当前尝试次数}/${最大尝试次数} 次尝试申请截图权限`);
                
                try {
                    // 先检查是否已有权限
                    if (typeof OCR !== 'undefined' && typeof OCR.检查截图权限 === 'function') {
                        已有权限 = OCR.检查截图权限();
                        if (已有权限) {
                            console.log("已有截图权限，无需重新申请");
                            break;
                        }
                    } else if (typeof 检查截图权限 === 'function') {
                        已有权限 = 检查截图权限();
                        if (已有权限) {
                            console.log("已有截图权限，无需重新申请");
                            break;
                        }
                    } else {
                        console.log("找不到检查截图权限函数，尝试直接申请权限");
                    }
                } catch (e) {
                    console.log("检查截图权限失败: " + e.message);
                }
                
                // 如果没有权限，尝试使用ROOT权限开启
                if (!已有权限 && 有ROOT权限) {
                    console.log("尝试使用ROOT权限开启截图权限");
                    已有权限 = 使用ROOT开启截图权限();
                    if (已有权限) {
                        console.log("使用ROOT权限成功开启截图权限");
                        break;
                    } else {
                        console.log("使用ROOT权限开启截图权限失败，尝试常规申请方式");
                    }
                }
                
                // 如果ROOT方式失败或没有ROOT权限，使用OCR模块的增强申请方法
                if (!已有权限 && typeof OCR !== 'undefined' && typeof OCR.申请截图权限_增强 === 'function') {
                    console.log("使用OCR模块的增强申请截图权限方法");
                    已有权限 = OCR.申请截图权限_增强(3); // 最多重试3次
                    if (已有权限) {
                        console.log("OCR增强方法申请截图权限成功");
                        break;
                    }
                }
                
                // 如果OCR增强方法失败，尝试使用标准方法
                if (!已有权限 && typeof requestScreenCapture === 'function') {
                    console.log("使用标准requestScreenCapture方法申请权限");
                    try {
                        requestScreenCapture();
                        sleep(2000); // 等待权限生效
                        
                        // 测试权限是否生效
                        try {
                            let testImg = captureScreen();
                            if (testImg) {
                                console.log("标准方法申请截图权限成功");
                                已有权限 = true;
                                try {
                                    testImg.recycle();
                                } catch (e) {
                                    // 忽略recycle错误
                                }
                                break;
                            }
                        } catch (e) {
                            console.log("截图测试失败: " + e.message);
                        }
                    } catch (e) {
                        console.error("标准方法申请截图权限失败: " + e.message);
                    }
                }
                
                // 如果仍然没有权限，等待后重试
                if (!已有权限 && 当前尝试次数 < 最大尝试次数) {
                    console.log("截图权限申请失败，等待后重试...");
                    sleep(2000);
                }
            }
            
            // 如果所有尝试都失败，提示用户并退出脚本
            if (!已有权限) {
                console.error("截图权限申请失败，无法继续执行脚本");
                if (typeof dialogs !== 'undefined') {
                    dialogs.alert(
                        "截图权限申请失败", 
                        "脚本需要截图权限才能正常工作。请手动授予截图权限后重新运行脚本。"
                    );
                } else {
                    toast("截图权限申请失败，请手动授予权限后重启脚本");
                    sleep(3000);
                }
                exit();
                return;
            }
        } catch (e) {
            console.error("申请截图权限过程中出错: " + e.message);
            console.error("错误堆栈: " + e.stack);
            if (typeof dialogs !== 'undefined') {
                dialogs.alert(
                    "截图权限申请出错", 
                    "申请截图权限过程中出现错误，脚本无法继续执行。\n错误详情: " + e.message
                );
            } else {
                toast("截图权限申请出错，请手动授予权限后重启脚本");
                sleep(3000);
            }
            exit();
            return;
        }
        
        // 显示当前操作模式
        var 当前模式 = DeviceOperation.获取当前模式();
        var 交互模式 = DeviceOperation.获取交互操作模式();
        var 窗口信息模式 = DeviceOperation.获取窗口信息模式();
        
        console.log("当前操作模式: " + (当前模式 === DeviceOperation.操作模式.无障碍 ? "无障碍" : 
                                    当前模式 === DeviceOperation.操作模式.HID ? "HID" : 
                                    当前模式 === DeviceOperation.操作模式.ROOT ? "ROOT" : "未知"));
        
        console.log("当前交互操作模式: " + (交互模式 === DeviceOperation.操作模式.无障碍 ? "无障碍" : 
                                      交互模式 === DeviceOperation.操作模式.HID ? "HID" : 
                                      交互模式 === DeviceOperation.操作模式.ROOT ? "ROOT" : "未知"));
        
        console.log("当前窗口信息模式: " + (窗口信息模式 === DeviceOperation.操作模式.无障碍 ? "无障碍" : 
                                      窗口信息模式 === DeviceOperation.操作模式.ROOT ? "ROOT" : "未知"));
    } else {
        console.error("DeviceOperation模块未加载，无法申请权限");
    }
    
    // 设置错误处理
    if (typeof ErrorHandler !== 'undefined' && typeof ErrorHandler.设置错误处理配置 === 'function') {
        ErrorHandler.设置错误处理配置({
            最大连续错误: 全局配置.最大连续错误,
            最大错误记录数: 全局配置.最大错误记录数,
            错误恢复策略: 全局配置.错误恢复策略
        });
    }
    
    // 启动垃圾回收定时器
    启动垃圾回收定时器();
    
    console.log("初始化完成，开始执行操作流程");
    
    // 启动整个操作流程
    整个操作流程();
}

/**
 * 定期执行垃圾回收
 */
function 启动垃圾回收定时器() {
    // 每15分钟强制执行一次垃圾回收
    setInterval(function () {
        console.log("执行垃圾回收...");
        try {
            // 使用Java系统垃圾回收（适用于AutoJS 6）
            java.lang.System.gc();
            console.log("垃圾回收完成");
        } catch (e) {
            console.error("垃圾回收失败: " + e.message);
        }
    }, 15 * 60 * 1000);

    // 注册exit事件，在脚本退出时执行清理
    if (typeof events !== 'undefined' && events.on) {
        events.on('exit', function () {
            console.log("脚本即将退出，执行最终清理工作");
            try {
                java.lang.System.gc();
                console.log("退出前垃圾回收完成");
            } catch (e) {
                console.error("退出前垃圾回收失败: " + e.message);
            }
        });
        console.log("已注册exit事件处理器");
    }
}

/**
 * 整个操作流程 - 根据配置项循环执行还原备份和小红书点赞操作
 */
function 整个操作流程() {
    console.log("开始执行整个操作流程");
    
    // 设置默认值
    var 操作几次恢复出厂设置 = 1; // 默认值
    
    console.log("将执行 " + 操作几次恢复出厂设置 + " 次还原备份和点赞操作，然后执行恢复出厂设置");
    // 当前执行次数
    var 当前执行次数 = 0;
    //重启飞行模式();
    //备份还原();
    //删除第一个备份();
    //开始小红书点赞操作();
    //开始小红书点赞操作();
    do {
        while(当前执行次数 < 操作几次恢复出厂设置) {
            重启飞行模式();
            备份还原();
            删除第一个备份();
            开始小红书点赞操作();
            当前执行次数++;
            // 从全局变量获取配置
            if (typeof global.操作几次恢复出厂设置 !== 'undefined' && global.操作几次恢复出厂设置 !== null) {
                操作几次恢复出厂设置 = global.操作几次恢复出厂设置;
                console.log("从全局变量获取配置: 操作几次恢复出厂设置 = " + 操作几次恢复出厂设置);
            }
        }
        
        恢复出厂设置();
        
    } while(true);
}

// 执行主函数
setTimeout(function () {
    console.log("延迟执行主函数，确保模块加载完成...");
    main();
}, 1000);