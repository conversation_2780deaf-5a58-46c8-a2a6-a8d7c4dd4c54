"ui";

// 配置文件路径
var CONFIG_FILE = "/sdcard/xiaohongshu_config.json";
var DEFAULT_URL_FILE = "/sdcard/xiaohongshu_url.txt";

// 当前URL文件路径
var 当前URL文件路径 = DEFAULT_URL_FILE;

/**
 * 读取配置文件获取URL文件路径
 */
function 读取配置文件() {
    try {
        if (!files.exists(CONFIG_FILE)) {
            console.log("配置文件不存在，使用默认URL文件路径");
            return DEFAULT_URL_FILE;
        }
        
        var 配置内容 = files.read(CONFIG_FILE);
        if (!配置内容) {
            console.log("配置文件为空，使用默认URL文件路径");
            return DEFAULT_URL_FILE;
        }
        
        var 配置对象 = JSON.parse(配置内容);
        var URL文件路径 = 配置对象.链接文件路径 || DEFAULT_URL_FILE;
        
        console.log("从配置文件读取URL文件路径: " + URL文件路径);
        return URL文件路径;
        
    } catch (e) {
        console.error("读取配置文件失败: " + e.message);
        return DEFAULT_URL_FILE;
    }
}

/**
 * 读取URL文件内容
 */
function 读取URL文件(文件路径) {
    try {
        if (!files.exists(文件路径)) {
            console.log("URL文件不存在，创建空文件: " + 文件路径);
            创建默认URL文件(文件路径);
            return "";
        }
        
        var 文件内容 = files.read(文件路径);
        console.log("成功读取URL文件: " + 文件路径);
        return 文件内容 || "";
        
    } catch (e) {
        console.error("读取URL文件失败: " + e.message);
        return "";
    }
}

/**
 * 保存URL文件内容
 */
function 保存URL文件(文件路径, 内容) {
    try {
        files.write(文件路径, 内容);
        console.log("成功保存URL文件: " + 文件路径);
        return true;
    } catch (e) {
        console.error("保存URL文件失败: " + e.message);
        return false;
    }
}

/**
 * 创建默认URL文件
 */
function 创建默认URL文件(文件路径) {
    var 默认内容 = "# 小红书链接文件\n" +
                 "# 请在下面添加小红书链接，每行一个\n" +
                 "# 支持的格式:\n" +
                 "# - https://www.xiaohongshu.com/explore/...\n" +
                 "# - https://xhslink.com/...\n" +
                 "# \n" +
                 "# 示例:\n" +
                 "# https://www.xiaohongshu.com/explore/123456789\n" +
                 "# https://xhslink.com/abcdef\n" +
                 "\n";
    
    files.write(文件路径, 默认内容);
}

/**
 * 统计URL数量
 */
function 统计URL数量(内容) {
    if (!内容) return 0;
    
    var 行列表 = 内容.split('\n');
    var URL数量 = 0;
    
    for (var i = 0; i < 行列表.length; i++) {
        var 行 = 行列表[i].trim();
        // 跳过空行和注释行
        if (行 && !行.startsWith('#')) {
            // 简单验证URL格式
            if (行.includes('xiaohongshu.com') || 行.includes('xhslink.com') || 行.startsWith('http')) {
                URL数量++;
            }
        }
    }
    
    return URL数量;
}

// 创建界面
ui.layout(
    <vertical>
        <text text="小红书链接管理" textSize="18sp" textColor="#333" gravity="center" margin="8 8 8 8"/>
        
        <card cardCornerRadius="6dp" cardElevation="2dp" margin="8 8 8 8">
            <vertical padding="12">
                <horizontal>
                    <text text="URL文件路径: " textSize="14sp" textColor="#666"/>
                    <text id="txt_file_path" textSize="14sp" textColor="#333" layout_weight="1"/>
                </horizontal>
                <horizontal margin="8 0 0 0">
                    <text text="URL数量: " textSize="14sp" textColor="#666"/>
                    <text id="txt_url_count" text="0" textSize="14sp" textColor="#333"/>
                </horizontal>
            </vertical>
        </card>
        
        <text text="URL链接内容:" textSize="14sp" textColor="#666" margin="8 8 4 8"/>
        
        <ScrollView layout_weight="1" margin="8 0 8 0">
            <input id="input_urls" 
                   hint="请输入小红书链接，每行一个&#10;支持格式:&#10;https://www.xiaohongshu.com/explore/...&#10;https://xhslink.com/..."
                   lines="20"
                   gravity="top"
                   textSize="12sp"
                   singleLine="false"/>
        </ScrollView>
        
        <horizontal margin="8 8 8 8">
            <button id="btn_save" text="保存" layout_weight="1" margin="0 4 0 0" textSize="14sp"/>
            <button id="btn_reload" text="重新加载" layout_weight="1" margin="0 4 0 0" textSize="14sp"/>
            <button id="btn_clear" text="清空" layout_weight="1" margin="0 4 0 0" textSize="14sp"/>
            <button id="btn_close" text="关闭" layout_weight="1" margin="0 0 0 4" textSize="14sp"/>
        </horizontal>
        
        <text id="status_text" text="就绪" textColor="#666" gravity="center" margin="4 4 4 8" textSize="12sp"/>
    </vertical>
);

/**
 * 加载URL内容
 */
function 加载URL内容() {
    try {
        // 读取配置文件获取URL文件路径
        当前URL文件路径 = 读取配置文件();
        
        // 显示文件路径
        ui.txt_file_path.setText(当前URL文件路径);
        
        // 读取URL文件内容
        var URL内容 = 读取URL文件(当前URL文件路径);
        
        // 显示在文本框中
        ui.input_urls.setText(URL内容);
        
        // 统计并显示URL数量
        var URL数量 = 统计URL数量(URL内容);
        ui.txt_url_count.setText(String(URL数量));
        
        ui.status_text.setText("已加载 " + URL数量 + " 个URL");
        console.log("URL内容加载完成，共 " + URL数量 + " 个URL");
        
    } catch (e) {
        console.error("加载URL内容失败: " + e.message);
        ui.status_text.setText("加载失败: " + e.message);
        toast("加载失败");
    }
}

/**
 * 保存URL内容
 */
function 保存URL内容() {
    try {
        var URL内容 = ui.input_urls.text();
        
        if (保存URL文件(当前URL文件路径, URL内容)) {
            // 重新统计URL数量
            var URL数量 = 统计URL数量(URL内容);
            ui.txt_url_count.setText(String(URL数量));
            
            ui.status_text.setText("保存成功，共 " + URL数量 + " 个URL");
            toast("保存成功");
        } else {
            ui.status_text.setText("保存失败");
            toast("保存失败");
        }
        
    } catch (e) {
        console.error("保存URL内容失败: " + e.message);
        ui.status_text.setText("保存失败: " + e.message);
        toast("保存失败");
    }
}

// 绑定事件
ui.btn_save.click(function() {
    保存URL内容();
});

ui.btn_reload.click(function() {
    加载URL内容();
    toast("已重新加载");
});

ui.btn_clear.click(function() {
    var confirmed = dialogs.confirm("确认清空", "是否清空所有URL内容？");
    if (confirmed) {
        ui.input_urls.setText("");
        ui.txt_url_count.setText("0");
        ui.status_text.setText("已清空内容");
        toast("已清空");
    }
});

ui.btn_close.click(function() {
    var confirmed = dialogs.confirm("确认关闭", "是否关闭URL管理界面？");
    if (confirmed) {
        ui.finish();
    }
});

// 文本变化时实时统计URL数量
ui.input_urls.addTextChangedListener({
    afterTextChanged: function(s) {
        var URL数量 = 统计URL数量(s.toString());
        ui.txt_url_count.setText(String(URL数量));
    }
});

// 初始化
console.log("小红书URL管理界面启动");
加载URL内容();
toast("URL管理界面已启动");
