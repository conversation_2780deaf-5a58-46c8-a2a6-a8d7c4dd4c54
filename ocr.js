/**
 * OCR模块 - 负责文字识别和相关操作
 * 作者: Claude
 * 日期: 2025-07-01
 */

// 检测是否在AutoJS环境中
var isAutoJsEnv = typeof auto !== 'undefined';

// OCR引擎类型
var OCR引擎类型 = {
    百度: "baidu",
    讯飞: "xunfei",
    谷歌: "google",
    内置: "builtin",
    自动: "auto"
};

// 当前OCR引擎配置
var 当前引擎 = OCR引擎类型.内置;
var 引擎配置 = {
    百度: {
        apiKey: "",
        secretKey: "",
        超时: 5000
    },
    讯飞: {
        appId: "",
        apiKey: "",
        超时: 5000
    },
    谷歌: {
        apiKey: "",
        超时: 5000
    },
    内置: {
        精度: "normal", // normal, high, low
        超时: 3000
    },
    自动: {
        超时: 3000
    }
};

// 缓存最近的识别结果
var 识别缓存 = {};
var 最大缓存数量 = 10;

// 修改截图缓存机制
let 当前截图 = null;
let 截图操作轮次 = 0; // 替代时间戳，用操作轮次标识
let 上次交互操作 = null; // 记录上次交互操作类型

// 统一模块引用
var DeviceOperation = require('./DeviceOperation.js');
// 确保require之后再定义操作模式和当前模式
var 操作模式 = DeviceOperation.操作模式;
var 当前模式 = DeviceOperation.获取交互操作模式();

/**
 * 获取屏幕截图，直接通过系统API获取
 * @param {Object} 外部截图 - 外部提供的截图对象
 * @param {number} 当前轮次 - 当前操作的轮次标识
 * @param {string} 上次操作 - 上一步执行的操作类型
 * @returns {Object} 截图对象
 */
function 获取屏幕截图(外部截图 = null, 当前轮次 = 0, 上次操作 = null) {
    // 如果提供了外部截图，直接使用
    if (外部截图) {
        console.log("使用外部提供的截图");
        return 外部截图;
    }

    try {
        console.log("ROOT模式下使用shell命令截图");
        let timestamp = Date.now();
        // 优先使用应用私有目录，避免权限问题
        let screenshotPath;
        if (typeof context !== 'undefined' && context.getExternalFilesDir) {
            // 使用应用私有外部存储目录
            screenshotPath = `${context.getExternalFilesDir(null)}/autojs_screenshot_${timestamp}.png`;
        } else {
            // 备用路径：使用/data/local/tmp（通常ROOT可访问）
            screenshotPath = `/data/local/tmp/autojs_screenshot_${timestamp}.png`;
        }
        console.log(`截图保存路径: ${screenshotPath}`);
        let result = shell(`su -c 'screencap -p ${screenshotPath}'`, true);

        if (result.code === 0) {
            console.log("截图命令执行成功，开始读取文件");
            try {
                // 检查文件是否存在
                let checkResult = shell(`ls -la ${screenshotPath}`, true);
                console.log(`文件检查结果: ${checkResult.result}`);

                let img = images.read(screenshotPath);
                // 删除临时文件
                shell(`rm ${screenshotPath}`, true);

                if (img) {
                    console.log("ROOT模式截图成功");
                    return img;
                } else {
                    console.error("ROOT模式截图文件读取失败 - images.read返回null");
                    console.error("可能原因：1.文件不存在 2.文件格式错误 3.权限不足");
                    return null;
                }
            } catch (e) {
                console.error("ROOT模式读取截图文件失败: " + e.message);
                console.error("错误堆栈: " + e.stack);
                shell(`rm ${screenshotPath}`, true); // 确保清理临时文件
                return null;
            }
        } else {
            console.error("ROOT模式截图命令执行失败");
            console.error(`命令返回码: ${result.code}`);
            console.error(`错误信息: ${result.error || '无错误信息'}`);
            console.error(`输出信息: ${result.result || '无输出信息'}`);
            return null;
        }
    } catch (e) {
        console.error("ROOT模式截图出错: " + e.message);
        return null;
    }
}

/**
 * 判断操作类型是否需要更新截图
 * @param {string} 操作类型 - 操作类型
 * @returns {boolean} 是否需要更新截图
 */
function 是需要更新的操作类型(操作类型) {
    // 这些操作会改变界面，需要重新截图
    var 需要更新的操作 = [
        "点击", "长按", "滑动", "返回", "输入", "粘贴", "home"
    ];

    return 操作类型 && 需要更新的操作.indexOf(操作类型) !== -1;
}

/**
 * 获取屏幕文字信息，支持关键词匹配
 * @param {string} 必须关键词 - 必须包含的关键词，多个关键词用|分隔
 * @param {string} 匹配关键词 - 匹配的关键词，多个关键词用|分隔
 * @param {number} 匹配数量 - 匹配的数量，0表示全部匹配
 * @param {string} 排除关键词 - 排除的关键词，多个关键词用|分隔
 * @param {Object} 截图 - 屏幕截图对象，如果为null则重新获取
 * @param {number} 当前轮次 - 当前OCR识别的轮次，用于避免重复识别
 * @param {string} 上次操作 - 上次执行的操作类型，用于判断是否需要更新截图
 * @returns {Array} 匹配的文字信息数组
 */
function 获取屏幕文字信息(必须关键词 = "", 匹配关键词 = "", 匹配数量 = 0, 排除关键词 = "", 截图 = null, 当前轮次 = 0, 上次操作 = null) {
    console.log("🔛🔛🔛====获取屏幕文字信息开始调用====🔛🔛🔛");
    console.log(`参数：必须关键词="${必须关键词}", 匹配关键词="${匹配关键词}", 匹配数量=${匹配数量}, 排除关键词="${排除关键词}"`);
    try {
        if(截图==true){
            截图=null
        }
        // 确保参数为字符串
        必须关键词 = String(必须关键词 || "");
        匹配关键词 = String(匹配关键词 || "");
        排除关键词 = String(排除关键词 || "");
        // 获取屏幕截图
        let img = 截图;
        if (!img) {
            img = 获取屏幕截图(null, 当前轮次, 上次操作);
            if (!img) {
                console.error("❌❌❌ 获取屏幕截图失败 ❌❌❌");
                return [];
            }
        } else {
            console.log("使用传入的有效截图");
        }
        // 调用OCR识别文字
        console.log("开始调用$ocr.detect方法获取所有文字...");
        let allTextWithPos = [];
        try {
            // 直接调用OCR，不管什么模式
            // ROOT模式下，图片来自ROOT截图，OCR引擎直接处理图片即可
            allTextWithPos = $ocr.detect(img);
            console.log(`OCR识别成功，共识别到 ${allTextWithPos.length} 个文字项`);

            if (allTextWithPos.length > 0) {
                console.log(`识别到的所有文字: ${allTextWithPos.map(item => `"${item.label}"`).join(", ")}`);
            } else {
                console.log("OCR未识别到任何文字");
                return [];
            }
        } catch (e) {
            console.error(`❌❌❌ OCR识别出错: ${e} ❌❌❌`);
            // 如果是权限相关错误，给出提示
            if (e.message && (e.message.includes("权限") || e.message.includes("permission") || e.message.includes("capture"))) {
                console.error("这是权限相关错误，请检查：");

                // 检查当前模式
                if (typeof global !== 'undefined' && typeof global.DeviceOperation !== 'undefined') {
                    let 当前模式 = global.DeviceOperation.获取当前模式();
                    if (当前模式 === global.DeviceOperation.操作模式.ROOT) {
                        console.error("ROOT模式下出现权限错误，这不应该发生！");
                        console.error("请检查：");
                        console.error("1. 设备是否正确ROOT");
                        console.error("2. AutoJS是否有ROOT权限");
                        console.error("3. 图片是否正确传入OCR引擎");
                    } else {
                        console.error("非ROOT模式下的权限错误：");
                        console.error("1. 请确保已授予截图权限");
                        console.error("2. 请确保无障碍服务已开启");
                        console.error("3. 重启应用后重新运行脚本");
                    }
                } else {
                    console.error("1. 请确保已授予截图权限");
                    console.error("2. 重启应用后重新运行脚本");
                }
            }

            // 🚫 完全禁用权限重新申请，避免弹窗
            console.log("❌ OCR识别失败，但为避免权限弹窗，不进行重试");
            console.log("❌ 如果是ROOT模式，请检查ROOT权限和截图功能");
            console.log("❌ 建议重启应用后重新运行脚本");
            return [];
        }

        // 处理关键词
        let 必须关键词数组 = 必须关键词 ? 必须关键词.split('|') : [];
        let 匹配关键词数组 = 匹配关键词 ? 匹配关键词.split('|') : [];
        let 排除关键词数组 = 排除关键词 ? 排除关键词.split('|') : [];

        console.log(`处理后的关键词：必须=[${必须关键词数组}], 匹配=[${匹配关键词数组}], 排除=[${排除关键词数组}]`);

        // 存储匹配结果
        let result = [];

        // 特殊处理"设置"关键词
        let 设置关键词处理 = false;
        if ((匹配关键词 && 匹配关键词.indexOf("设置") !== -1) ||
            (必须关键词 && 必须关键词.indexOf("设置") !== -1)) {
            console.log("检测到设置关键词，启用特殊处理");
            设置关键词处理 = true;
        }

        // 处理排除关键词
        if (排除关键词数组.length > 0) {
            console.log(`📊检查排除关键词: "${排除关键词}"`);
            for (let 关键词 of 排除关键词数组) {
                let 找到排除关键词 = false;

                for (let item of allTextWithPos) {
                    if (匹配文本与关键词(item.label, 关键词)) {
                        console.log(`发现排除关键词"${关键词}"匹配文本"${item.label}"，返回空结果`);
                        找到排除关键词 = true;
                        break;
                    }
                }

                if (找到排除关键词) {
                    return [];
                }
            }
            console.log("未找到任何排除关键词，继续处理");
        }

        // 处理必须关键词
        if (必须关键词数组.length > 0) {
            console.log(`开始处理${必须关键词数组.length}个必须关键词`);
            let 必须关键词匹配计数 = 0;
            let 必须关键词匹配详情 = [];

            for (let 关键词 of 必须关键词数组) {
                console.log(`检查必须关键词: "${关键词}"`);
                let 当前关键词匹配 = false;

                for (let item of allTextWithPos) {
                    // 添加通配符匹配支持
                    if (匹配文本与关键词(item.label, 关键词)) {
                        console.log(`必须关键词"${关键词}"匹配成功: "${item.label}"`);
                        当前关键词匹配 = true;
                        必须关键词匹配计数++;
                        必须关键词匹配详情.push(`"${关键词}" 匹配 "${item.label}"`);

                        let position = 解析位置信息(item.bounds);
                        if (position) {
                            result.push({
                                text: item.label,
                                confidence: item.confidence || 0.9,
                                position: position,
                                matchType: "必须关键词",
                                keyword: 关键词
                            });
                        }
                    }
                }

                if (!当前关键词匹配) {
                    console.log(`未找到必须关键词"${关键词}"的匹配`);
                }
            }

            if (必须关键词匹配计数 === 0) {
                console.log("未找到任何必须关键词，返回空结果");
                return [];
            } else {
                console.log(`必须关键词匹配情况: ${必须关键词匹配详情.join(', ')}`);
            }
        }

        // 处理匹配关键词
        if (匹配关键词数组.length > 0) {
            console.log(`开始处理${匹配关键词数组.length}个匹配关键词`);
            // 记录已处理的文字，避免重复
            let processedItems = new Set();

            for (let 关键词 of 匹配关键词数组) {
                console.log(`检查匹配关键词: "${关键词}"`);
                let 当前关键词匹配计数 = 0;

                for (let item of allTextWithPos) {
                    // 如果已处理过此项，跳过
                    if (processedItems.has(item.label)) continue;

                    // 检查是否匹配，使用通配符匹配
                    if (匹配文本与关键词(item.label, 关键词)) {
                        console.log(`匹配关键词"${关键词}"匹配成功: "${item.label}"`);
                        当前关键词匹配计数++;
                        processedItems.add(item.label);

                        let position = 解析位置信息(item.bounds);
                        if (position) {
                            result.push({
                                text: item.label,
                                confidence: item.confidence || 0.9,
                                position: position,
                                matchType: "匹配关键词",
                                keyword: 关键词
                            });

                            // 如果已达到匹配数量，提前返回
                            if (匹配数量 > 0 && result.length >= 匹配数量) {
                                console.log(`已匹配到 ${result.length} 个文字，达到指定数量 ${匹配数量}，提前返回`);
                                return result;
                            }
                        }
                    }
                }

                console.log(`关键词"${关键词}"匹配到${当前关键词匹配计数}个结果`);
            }
        }

        // 特殊处理设置关键词
        if (设置关键词处理 && result.length === 0) {
            console.log("使用设置关键词的特殊处理流程");

            // 查找任何可能是设置的文字
            for (let item of allTextWithPos) {
                let label = item.label;

                // 检查各种设置形式
                if (label === "设置" ||
                    label.indexOf("设置") !== -1 ||
                    label === "设 置" ||
                    label.indexOf("设 置") !== -1 ||
                    label.toLowerCase() === "settings" ||
                    label.toLowerCase().indexOf("settings") !== -1) {

                    console.log(`发现设置关键词: "${label}"`);
                    let position = 解析位置信息(item.bounds);
                    if (position) {
                        result.push({
                            text: label,
                            confidence: item.confidence || 0.9,
                            position: position,
                            matchType: "设置特殊匹配",
                            keyword: "设置"
                        });
                    }
                }
            }
        }

        console.log(`获取屏幕文字信息完成，共匹配到 ${result.length} 个结果`);
        if (result.length > 0) {
            console.log("匹配结果:");
            for (let i = 0; i < Math.min(result.length, 5); i++) {
                console.log(`  ${i + 1}. "${result[i].text}" (${result[i].matchType})`);
            }
            if (result.length > 5) {
                console.log(`  ...以及其他 ${result.length - 5} 个结果`);
            }
        }

        return result;
    } catch (e) {
        console.error(`❌❌❌ OCR识别出错: ${e} ❌❌❌`);
        console.error(e.stack);
        return [];
    } finally {
        console.log("🔚🔚🔚====获取屏幕文字信息调用结束====🔚🔚🔚");
        // 不要释放img如果它是外部传入的截图或当前截图
        // 这部分由调用者管理
    }
}

/**
 * 解析OCR返回的位置信息
 * @param {Object} bounds - OCR返回的bounds对象
 * @returns {Object} 标准化的位置信息
 */
function 解析位置信息(bounds) {
    if (!bounds) return null;

    let position = null;
    let boundsStr = bounds.toString();
    let matches = boundsStr.match(/Rect\((\d+),\s*(\d+)\s*-\s*(\d+),\s*(\d+)\)/);

    if (matches && matches.length >= 5) {
        return {
            left: parseInt(matches[1]),
            top: parseInt(matches[2]),
            right: parseInt(matches[3]),
            bottom: parseInt(matches[4]),
            centerX: (parseInt(matches[1]) + parseInt(matches[3])) / 2,
            centerY: (parseInt(matches[2]) + parseInt(matches[4])) / 2,
            width: parseInt(matches[3]) - parseInt(matches[1]),
            height: parseInt(matches[4]) - parseInt(matches[2])
        };
    }

    return null;
}

/**
 * 点击指定文字坐标
 * 查找并点击指定文字，支持通配符和正则表达式
 * @param {Array|null} 识别结果 - 获取屏幕文字信息的返回结果，若为null则重新识别
 * @param {string} 匹配文字 - 要匹配并点击的文字，多个备选文字用|分隔
 * @param {number} 点击序号 - 如果有多个相同文字，点击第几个，从1开始计数，默认为1
 * @param {string} 排除文字 - 要排除的文字，存在则不进行点击，多个文字用|分隔
 * @param {number} 偏移X - X轴偏移量，正数向右偏移，负数向左偏移，默认为0
 * @param {number} 偏移Y - Y轴偏移量，正数向下偏移，负数向上偏移，默认为0
 * @returns {boolean} 是否成功点击
 */
function 点击指定文字坐标(识别结果 = null, 匹配文字 = "", 点击序号 = 1, 排除文字 = "", 偏移X = 0, 偏移Y = 0) {
    // 参数检查：如果没有提供识别结果且没有匹配关键词，则无法执行
    if (!识别结果 && !匹配文字) return false;
    if (点击序号 < 1) 点击序号 = 1;

    // 确保所有关键词参数都是字符串
    匹配文字 = String(匹配文字 || "");
    排除文字 = String(排除文字 || "");

    // 确保偏移量是数字
    偏移X = Number(偏移X) || 0;
    偏移Y = Number(偏移Y) || 0;

    console.log(`[调试] 准备点击文字: "${匹配文字}", 序号: ${点击序号}, 排除文字: "${排除文字}", 偏移: (${偏移X}, ${偏移Y})`);

    // 特殊处理"设置"关键词，确保能正确匹配
    let 特殊处理设置关键词 = false;
    if (匹配文字 === "设置" || 匹配文字.indexOf("设置") !== -1) {
        console.log("[调试] 检测到设置关键词，启用特殊处理模式");
        特殊处理设置关键词 = true;
    }

    // 如果没有提供识别结果，则重新获取
    let 文字数据 = 识别结果;
    if (!文字数据) {
        console.log(`[调试] 重新获取屏幕文字信息，查找: "${匹配文字}"`);

        // 只需要识别到点击序号数量的文字即可，优化性能
        文字数据 = 获取屏幕文字信息("", 匹配文字, 点击序号, 排除文字);

        // 如果是特殊关键词"设置"且没有匹配结果，尝试使用精确匹配
        if (特殊处理设置关键词 && (!文字数据 || 文字数据.length === 0)) {
            console.log("[调试] 设置关键词未匹配，尝试使用精确匹配模式");
            // 获取所有文字，然后手动过滤
            let 所有文字 = 获取所有文字();
            文字数据 = [];

            // 手动查找"设置"文字
            for (let i = 0; i < 所有文字.length; i++) {
                let item = 所有文字[i];
                if (匹配文本与关键词(item.text, "设置")) {
                    console.log(`[调试] 找到精确匹配的设置文字: "${item.text}"`);
                    文字数据.push({
                        text: item.text,
                        position: 解析位置信息(item.bounds),
                        matchType: "精确匹配",
                        keyword: "设置"
                    });
                }
            }
        }
    }

    // 如果没有找到任何文字，返回失败
    if (!文字数据 || 文字数据.length === 0) {
        console.error(`[调试] 未找到匹配的文字: "${匹配文字}"`);
        // 输出当前屏幕所有文字，方便调试
        console.log("[调试] 尝试再次显示所有文字...");
        调试显示所有文字();
        return false;
    }

    console.log(`[调试] 共找到 ${文字数据.length} 个可点击的文字`);

    // 显示找到的每个匹配项
    for (let i = 0; i < 文字数据.length; i++) {
        let item = 文字数据[i];
        console.log(`[调试] 匹配项 ${i + 1}: 文字="${item.text}", 匹配方式=${item.matchType || "文本匹配"}, 关键词="${item.keyword || 匹配文字}", 位置=(${Math.round(item.position.centerX)},${Math.round(item.position.centerY)})`);
    }

    // 所有识别到的文字都是可点击的
    let 可点击文字 = 文字数据;

    // 如果没有提供匹配关键词但有识别结果，显示提示信息
    if (!匹配文字 && 文字数据.length > 0) {
        console.log("[调试] 没有提供匹配关键词，将使用所有匹配文字");
    }

    // 如果找到了足够数量的匹配项
    if (可点击文字.length >= 点击序号) {
        // 获取要点击的项
        let 目标项 = 可点击文字[点击序号 - 1];

        // 点击目标项的中心位置，加上偏移量
        let centerX = 目标项.position.centerX + 偏移X;
        let centerY = 目标项.position.centerY + 偏移Y;

        console.log(`[调试] 点击文字: "${目标项.text}" (${目标项.matchType || "文本匹配"}), 关键词: "${目标项.keyword || 匹配文字}", 原始位置: (${Math.round(目标项.position.centerX)}, ${Math.round(目标项.position.centerY)}), 偏移后位置: (${Math.round(centerX)}, ${Math.round(centerY)})`);

        try {
            // 使用DeviceOperation模块的点击函数
            DeviceOperation.点击(centerX, centerY);
            console.log(`[调试] 点击执行完成: (${Math.round(centerX)}, ${Math.round(centerY)})`);

            // 点击后等待1秒，让UI有反应的时间
            DeviceOperation.sleep(1000);
            return true;
        } catch (e) {
            console.error(`[调试] 点击执行失败: ${e.message}`);
            return false;
        }
    }

    // 未找到足够数量的匹配项
    console.error(`[调试] 未找到可点击的匹配文字: "${匹配文字}" 或序号 ${点击序号} 超出范围`);
    return false;
}

/**
 * 判断屏幕包含文字
 * 判断屏幕上是否包含指定数量的关键词
 * @param {Array|null} 识别结果 - 获取屏幕文字信息的返回结果，若为null则重新识别
 * @param {string} 必须关键词 - 必须匹配到的关键词，多个关键词用|分隔
 * @param {string} 匹配关键词 - 要匹配的关键词，多个关键词用|分隔
 * @param {number} 匹配数量 - 至少需要匹配的关键词数量，默认为1
 * @param {string} 排除关键词 - 要排除的关键词，多个关键词用|分隔
 * @returns {boolean} 是否找到足够数量的关键词
 */
function 判断屏幕包含文字(识别结果 = null, 必须关键词 = "", 匹配关键词 = "", 匹配数量 = 1, 排除关键词 = "") {
    // 如果没有提供识别结果，则重新获取
    let 文字数据 = 识别结果;
    if (!文字数据) {
        文字数据 = 获取屏幕文字信息(必须关键词, 匹配关键词, 匹配数量, 排除关键词);
    }

    // 如果有必须关键词但识别结果为空，返回失败
    if (必须关键词 && (!Array.isArray(文字数据) || 文字数据.length === 0)) {
        return false;
    }

    // 直接使用获取屏幕文字信息的结果，因为它已经处理了所有条件
    return 文字数据.length >= 匹配数量;
}

/**
 * 查找文字位置
 * 查找文字在屏幕上的位置
 * @param {string} 文本 - 要查找的文字
 * @param {boolean} 精确匹配 - 是否精确匹配
 * @param {Image} 图片 - 要识别的图片，为null则截屏
 * @returns {Object|null} 文字位置信息，未找到返回null
 */
function 查找文字位置(文本, 精确匹配 = true, 图片 = null) {
    // 确保文本是字符串
    文本 = String(文本 || "");
    if (!文本) return null;

    // 获取屏幕文字信息
    let 识别结果 = 获取屏幕文字信息("", 文本, 1, "", 图片);

    if (识别结果 && 识别结果.length > 0) {
        return 识别结果[0].position;
    }

    return null;
}

/**
 * 释放OCR资源
 */
function 释放OCR资源() {
    // 释放当前缓存的截图
    if (当前截图) {
        try {
            当前截图.recycle();
            当前截图 = null;
            截图操作轮次 = 0;
            上次交互操作 = null;
            console.log("释放OCR截图缓存");
        } catch (e) {
            console.error("释放OCR截图缓存失败:", e);
        }
    }
}

/**
 * 获取所有文字
 * 获取屏幕上的所有文字，用于调试
 * @param {Image} [截图=null] - 要识别的图片，默认为当前屏幕截图
 * @param {number} [当前轮次=0] - 当前操作的轮次标识
 * @param {string} [上次操作=null] - 上一步执行的操作类型
 * @returns {Array} 识别结果数组
 */
function 获取所有文字(截图 = null, 当前轮次 = 0, 上次操作 = null) {
    console.log("[获取所有文字] 开始执行...");

    // 获取有效截图
    let img = null;
    try {
        // 先尝试使用传入的截图或缓存的截图
        if (截图 && !截图.isRecycled()) {
            console.log("[获取所有文字] 使用传入的有效截图");
            img = 截图;
        } else if (当前截图 && !当前截图.isRecycled()) {
            console.log("[获取所有文字] 使用缓存的有效截图");
            img = 当前截图;
        } else {
            // 创建新截图
            console.log("[获取所有文字] 创建全新截图...");
            img = 获取屏幕截图(null, 当前轮次, 上次操作);

            // 更新当前截图缓存
            if (当前截图) {
                try {
                    当前截图.recycle();
                } catch (e) {
                    console.error("[获取所有文字] 释放旧截图失败:", e);
                }
            }
            当前截图 = img;
            截图操作轮次 = 当前轮次;
            上次交互操作 = 上次操作;
        }

        if (!img) {
            console.error("[获取所有文字] 截图获取失败");
            return [];
        }

        // 再次检查截图是否有效
        if (img.isRecycled()) {
            console.log("[获取所有文字] 截图资源已被回收，重新创建截图");
            img = 获取屏幕截图();
            当前截图 = img;
        }
    } catch (e) {
        console.error("[获取所有文字] 获取屏幕截图失败:", e);

        // 尝试重新创建新截图
        try {
            console.log("[获取所有文字] 尝试重新创建截图...");
            img = 获取屏幕截图();
            当前截图 = img;
        } catch (e2) {
            console.error("[获取所有文字] 重新创建截图也失败:", e2);
            return [];
        }
    }

    if (!img) {
        console.error("[获取所有文字] 截图仍为空");
        return [];
    }

    try {
        // 尝试多种方式获取OCR结果
        let result = null;

        // 首先尝试使用$ocr函数
        try {
            console.log("[获取所有文字] 尝试使用$ocr方法");
            result = $ocr(img);
            if (result && Array.isArray(result) && result.length > 0) {
                console.log(`[获取所有文字] $ocr方法成功，获取到 ${result.length} 个文字`);
            } else {
                console.log("[获取所有文字] $ocr方法未返回结果，尝试其他方法");
                result = null;
            }
        } catch (e) {
            console.error("[获取所有文字] $ocr方法出错:", e);
            result = null;
        }

        // 如果第一种方法失败，尝试使用$ocr.detect
        if (!result) {
            try {
                console.log("[获取所有文字] 尝试使用$ocr.detect方法");
                let detectResult = $ocr.detect(img);
                if (detectResult && Array.isArray(detectResult) && detectResult.length > 0) {
                    console.log(`[获取所有文字] $ocr.detect方法成功，获取到 ${detectResult.length} 个文字项`);
                    // 从detect结果中提取文本
                    result = detectResult.map(item => item.label);
                    console.log(`[获取所有文字] 提取后得到 ${result.length} 个文字`);
                } else {
                    console.log("[获取所有文字] $ocr.detect方法未返回结果");
                    result = [];
                }
            } catch (e) {
                console.error("[获取所有文字] $ocr.detect方法出错:", e);
                result = [];
            }
        }

        // 输出所有识别到的文字，用于调试
        if (result && result.length > 0) {
            console.log(`[获取所有文字] 共识别到 ${result.length} 个文字`);
            // 显示所有文字，不限制数量
            console.log(`[获取所有文字] 全部识别到的文字: ${result.map(t => `"${t}"`).join(', ')}`);

            // 特别检查是否包含"设置"关键词
            let 设置文字 = result.filter(t => 匹配文本与关键词(t, "设置"));
            if (设置文字.length > 0) {
                console.log(`[获取所有文字] 找到包含"设置"的文字: ${设置文字.join(', ')}`);
            } else {
                console.log(`[获取所有文字] 未找到包含"设置"的文字`);
            }
        } else {
            console.log("[获取所有文字] 未识别到任何文字");
            result = [];
        }

        return result;
    } catch (e) {
        console.error("[获取所有文字] OCR识别出错:", e);
        return [];
    }
}

/**
 * 初始化OCR引擎 (已优化，避免权限弹窗)
 * @returns {boolean} 初始化是否成功
 */
function 初始化OCR() {
    try {
        // 检查当前操作模式
        let 当前操作模式 = null;
        if (typeof DeviceOperation !== 'undefined' && typeof DeviceOperation.获取当前模式 === 'function') {
            当前操作模式 = DeviceOperation.获取当前模式();
        }

        // ROOT模式下完全跳过任何可能触发权限的操作
        if (当前操作模式 === 操作模式.ROOT || 当前操作模式 === 3) {
            console.log("ROOT模式下完全跳过OCR初始化测试，避免权限弹窗");
            return true;
        }

        // 非ROOT模式下也跳过截图测试，避免权限弹窗
        console.log("为避免权限弹窗，跳过OCR初始化截图测试");
        console.log("OCR功能将在首次使用时进行实际测试");
        return true;
    } catch (e) {
        console.error("OCR初始化失败:", e);
        return false;
    }
}

// 标记初始化状态
var isInitialized = false;
var isInitializing = false;

// 禁用自动初始化，避免在模块加载时触发权限申请
// setTimeout(function() {
//     初始化OCR();
//     isInitialized = true;
// }, 100);
console.log("OCR模块已加载，但跳过自动初始化以避免权限弹窗");

/**
 * 设置OCR引擎
 * @param {string} 引擎类型 - OCR引擎类型
 * @param {Object} 配置 - 引擎配置
 * @returns {boolean} 是否设置成功
 */
function 设置OCR引擎(引擎类型, 配置 = {}) {
    try {
        // 检查引擎类型是否有效
        if (!Object.values(OCR引擎类型).includes(引擎类型)) {
            console.error(`无效的OCR引擎类型: ${引擎类型}`);
            return false;
        }

        // 更新当前引擎
        当前引擎 = 引擎类型;

        // 更新引擎配置
        if (配置 && typeof 配置 === 'object') {
            引擎配置[引擎类型] = Object.assign({}, 引擎配置[引擎类型], 配置);
        }

        console.log(`OCR引擎已设置为: ${引擎类型}`);
        return true;
    } catch (error) {
        console.error(`设置OCR引擎出错: ${error.message}`);
        return false;
    }
}

/**
 * 识别文字
 * @param {Image} 图像 - 要识别的图像
 * @param {Object} 选项 - 识别选项
 * @param {boolean} [选项.使用缓存=true] - 是否使用缓存
 * @param {string} [选项.语言="zh-Hans"] - 识别语言
 * @returns {Promise<Array>} 识别结果数组
 */
function 识别文字(图像, 选项 = {}) {
    return new Promise(function (resolve, reject) {
        try {
            if (!图像) {
                console.error("图像为空");
                resolve([]);
                return;
            }

            // 默认选项
            var 默认选项 = {
                使用缓存: true,
                语言: "zh-Hans"
            };

            // 合并选项
            var 最终选项 = Object.assign({}, 默认选项, 选项);

            // 计算图像哈希值作为缓存键
            var 图像哈希 = `${图像.getWidth()}_${图像.getHeight()}_${Date.now()}`;

            // 检查缓存
            if (最终选项.使用缓存 && 识别缓存[图像哈希]) {
                console.log("使用缓存的OCR结果");
                resolve(识别缓存[图像哈希]);
                return;
            }

            if (!isAutoJsEnv) {
                // 非AutoJS环境，返回模拟结果
                console.log("非AutoJS环境，返回模拟OCR结果");
                var 模拟结果 = [
                    {
                        text: "首页",
                        confidence: 0.95,
                        bounds: {
                            left: 100,
                            top: 1200,
                            right: 200,
                            bottom: 1250,
                            centerX: 150,
                            centerY: 1225
                        }
                    },
                    {
                        text: "关注",
                        confidence: 0.92,
                        bounds: {
                            left: 300,
                            top: 1200,
                            right: 400,
                            bottom: 1250,
                            centerX: 350,
                            centerY: 1225
                        }
                    },
                    {
                        text: "发现",
                        confidence: 0.90,
                        bounds: {
                            left: 500,
                            top: 1200,
                            right: 600,
                            bottom: 1250,
                            centerX: 550,
                            centerY: 1225
                        }
                    },
                    {
                        text: "消息",
                        confidence: 0.88,
                        bounds: {
                            left: 700,
                            top: 1200,
                            right: 800,
                            bottom: 1250,
                            centerX: 750,
                            centerY: 1225
                        }
                    }
                ];

                // 缓存模拟结果
                if (最终选项.使用缓存) {
                    识别缓存[图像哈希] = 模拟结果;
                }

                resolve(模拟结果);
                return;
            }

            // 根据当前引擎执行识别
            switch (当前引擎) {
                case OCR引擎类型.内置:
                    使用内置引擎识别(图像, 最终选项)
                        .then(处理识别结果);
                    break;

                case OCR引擎类型.百度:
                    使用百度引擎识别(图像, 最终选项)
                        .then(处理识别结果);
                    break;

                case OCR引擎类型.讯飞:
                    使用讯飞引擎识别(图像, 最终选项)
                        .then(处理识别结果);
                    break;

                case OCR引擎类型.谷歌:
                    使用谷歌引擎识别(图像, 最终选项)
                        .then(处理识别结果);
                    break;

                case OCR引擎类型.自动:
                    使用自动引擎识别(图像, 最终选项)
                        .then(处理识别结果);
                    break;

                default:
                    console.error(`未知的OCR引擎类型: ${当前引擎}`);
                    resolve([]);
                    break;
            }

            // 处理识别结果的函数
            function 处理识别结果(识别结果) {
                // 缓存识别结果
                if (最终选项.使用缓存) {
                    识别缓存[图像哈希] = 识别结果;

                    // 限制缓存大小
                    var 缓存键列表 = Object.keys(识别缓存);
                    if (缓存键列表.length > 最大缓存数量) {
                        delete 识别缓存[缓存键列表[0]];
                    }
                }

                resolve(识别结果);
            }
        } catch (error) {
            console.error(`识别文字出错: ${error.message}`);
            resolve([]);
        }
    });
}

/**
 * 使用自动引擎识别
 * @param {Image} 图像 - 要识别的图像
 * @param {Object} 选项 - 识别选项
 * @returns {Promise<Array>} 识别结果数组
 */
function 使用自动引擎识别(图像, 选项) {
    return new Promise(function (resolve, reject) {
        try {
            console.log("使用自动OCR引擎识别");

            // 直接使用$ocr.detect方法获取文字与位置
            let 识别结果 = $ocr.detect(图像);
            if (!识别结果 || !Array.isArray(识别结果)) {
                console.error("OCR识别失败或返回格式异常");
                resolve([]);
                return;
            }

            console.log(`自动OCR识别成功，找到 ${识别结果.length} 个文字项`);

            // 格式化识别结果，确保每个项都有一致的结构
            let 结果数组 = 识别结果.map(function (item) {
                // 确保bounds属性存在
                let bounds = item.bounds || null;

                return {
                    text: item.label || "",
                    confidence: item.confidence || 0.9,
                    bounds: bounds
                };
            });

            resolve(结果数组);
        } catch (error) {
            console.error(`自动OCR识别出错: ${error.message}`);
            // 出错时尝试使用内置引擎
            使用内置引擎识别(图像, 选项)
                .then(function (内置结果) {
                    resolve(内置结果);
                })
                .catch(function () {
                    resolve([]);
                });
        }
    });
}

/**
 * 使用内置引擎识别
 * @param {Image} 图像 - 要识别的图像
 * @param {Object} 选项 - 识别选项
 * @returns {Promise<Array>} 识别结果数组
 */
function 使用内置引擎识别(图像, 选项) {
    return new Promise(function (resolve, reject) {
        try {
            console.log("使用内置OCR引擎识别");

            // 直接使用内置引擎的$ocr.detect方法
            let 识别结果 = $ocr.detect(图像);
            if (!识别结果 || !Array.isArray(识别结果)) {
                console.error("内置OCR识别失败或返回格式异常");
                resolve([]);
                return;
            }

            console.log(`内置OCR识别成功，找到 ${识别结果.length} 个文字项`);

            // 输出所有识别到的文字内容（单行）
            let 所有文字 = 识别结果.map(item => `"${item.label}"`).join(', ');
            console.log(`===== 所有识别到的文字内容: ${所有文字} =====`);

            // 格式化识别结果，确保每个项都有一致的结构
            let 结果数组 = 识别结果.map(function (item) {
                // 确保bounds属性存在
                let bounds = item.bounds || null;

                return {
                    text: item.label || "",
                    confidence: item.confidence || 0.9,
                    bounds: bounds
                };
            });

            resolve(结果数组);
        } catch (error) {
            console.error(`内置OCR引擎识别出错: ${error.message}`);

            // 尝试使用简单的OCR方法作为后备
            try {
                let 简单识别结果 = $ocr(图像);
                if (Array.isArray(简单识别结果)) {
                    console.log(`简单OCR识别成功，找到 ${简单识别结果.length} 个文字`);

                    // 简单OCR方法无位置信息，创建虚拟位置
                    let 屏幕宽度 = 图像.getWidth();
                    let 屏幕高度 = 图像.getHeight();

                    let 结果数组 = 简单识别结果.map(function (text, index) {
                        // 创建一个虚拟的位置信息
                        let y = 100 + index * 50;
                        return {
                            text: text,
                            confidence: 0.8,
                            bounds: {
                                left: 100,
                                top: y,
                                right: 屏幕宽度 - 100,
                                bottom: y + 40
                            }
                        };
                    });

                    resolve(结果数组);
                    return;
                }
            } catch (e) {
                console.error(`简单OCR识别出错: ${e.message}`);
            }

            resolve([]);
        }
    });
}

/**
 * 使用百度引擎识别
 * @param {Image} 图像 - 要识别的图像
 * @param {Object} 选项 - 识别选项
 * @returns {Promise<Array>} 识别结果数组
 */
function 使用百度引擎识别(图像, 选项) {
    return new Promise(function (resolve, reject) {
        try {
            console.log("使用百度OCR引擎识别...");

            // 获取百度引擎配置
            var 引擎配置项 = 引擎配置[OCR引擎类型.百度] || {};

            if (!引擎配置项.apiKey || !引擎配置项.secretKey) {
                console.error("百度OCR引擎配置不完整");
                resolve([]);
                return;
            }

            // TODO: 实现百度OCR API调用
            console.error("百度OCR引擎尚未实现");
            resolve([]);
        } catch (error) {
            console.error(`百度OCR引擎识别出错: ${error.message}`);
            resolve([]);
        }
    });
}

/**
 * 使用讯飞引擎识别
 * @param {Image} 图像 - 要识别的图像
 * @param {Object} 选项 - 识别选项
 * @returns {Promise<Array>} 识别结果数组
 */
function 使用讯飞引擎识别(图像, 选项) {
    return new Promise(function (resolve, reject) {
        try {
            console.log("使用讯飞OCR引擎识别...");

            // 获取讯飞引擎配置
            var 引擎配置项 = 引擎配置[OCR引擎类型.讯飞] || {};

            if (!引擎配置项.appId || !引擎配置项.apiKey) {
                console.error("讯飞OCR引擎配置不完整");
                resolve([]);
                return;
            }

            // TODO: 实现讯飞OCR API调用
            console.error("讯飞OCR引擎尚未实现");
            resolve([]);
        } catch (error) {
            console.error(`讯飞OCR引擎识别出错: ${error.message}`);
            resolve([]);
        }
    });
}

/**
 * 使用谷歌引擎识别
 * @param {Image} 图像 - 要识别的图像
 * @param {Object} 选项 - 识别选项
 * @returns {Promise<Array>} 识别结果数组
 */
function 使用谷歌引擎识别(图像, 选项) {
    return new Promise(function (resolve, reject) {
        try {
            console.log("使用谷歌OCR引擎识别...");

            // 获取谷歌引擎配置
            var 引擎配置项 = 引擎配置[OCR引擎类型.谷歌] || {};

            if (!引擎配置项.apiKey) {
                console.error("谷歌OCR引擎配置不完整");
                resolve([]);
                return;
            }

            // TODO: 实现谷歌OCR API调用
            console.error("谷歌OCR引擎尚未实现");
            resolve([]);
        } catch (error) {
            console.error(`谷歌OCR引擎识别出错: ${error.message}`);
            resolve([]);
        }
    });
}

/**
 * 查找文字
 * @param {Array} 识别结果 - OCR识别结果数组
 * @param {Object|string|Array} 查找选项 - 查找选项或关键词
 * @param {string|Array} [查找选项.关键词] - 要查找的关键词或关键词列表
 * @param {string|Array} [查找选项.匹配关键词] - 匹配条件关键词或关键词列表
 * @param {number} [查找选项.匹配数量] - 需要匹配的关键词数量
 * @param {string|Array} [查找选项.排除关键词] - 排除条件关键词或关键词列表
 * @param {boolean} [查找选项.模糊匹配] - 是否使用模糊匹配
 * @param {number} [查找选项.模糊阈值] - 模糊匹配阈值(0-1)
 * @returns {Object} 匹配的文字对象
 */
function 查找文字(识别结果, 查找选项, 匹配关键词, 匹配数量, 排除关键词) {
    try {
        if (!识别结果 || !Array.isArray(识别结果) || 识别结果.length === 0) {
            console.error("无效的识别结果");
            return null;
        }

        // 处理参数兼容性
        var 关键词, 模糊匹配 = false, 模糊阈值 = 0.7;

        // 如果第二个参数是对象，使用新的参数格式
        if (查找选项 && typeof 查找选项 === 'object' && !Array.isArray(查找选项)) {
            关键词 = 查找选项.关键词;
            匹配关键词 = 查找选项.匹配关键词 || [];
            匹配数量 = 查找选项.匹配数量 || 0;
            排除关键词 = 查找选项.排除关键词 || [];
            模糊匹配 = 查找选项.模糊匹配 || false;
            模糊阈值 = 查找选项.模糊阈值 || 0.7;
        } else {
            // 兼容旧的参数格式
            关键词 = 查找选项;
            匹配关键词 = 匹配关键词 || [];
            匹配数量 = 匹配数量 || 0;
            排除关键词 = 排除关键词 || [];
        }

        // 确保关键词是数组
        if (!Array.isArray(关键词)) {
            关键词 = [关键词];
        }

        // 确保匹配关键词是数组
        if (!Array.isArray(匹配关键词)) {
            匹配关键词 = [匹配关键词];
        }

        // 确保排除关键词是数组
        if (!Array.isArray(排除关键词)) {
            排除关键词 = [排除关键词];
        }

        // 过滤空字符串
        关键词 = 关键词.filter(item => item);
        匹配关键词 = 匹配关键词.filter(item => item);
        排除关键词 = 排除关键词.filter(item => item);

        if (关键词.length === 0) {
            console.error("关键词为空");
            return null;
        }

        // 遍历识别结果查找匹配项
        for (var i = 0; i < 识别结果.length; i++) {
            var 项目 = 识别结果[i];
            var 文本 = 项目.text;

            // 检查是否匹配关键词
            var 匹配关键词数 = 0;
            var 是否匹配 = false;

            // 检查主关键词
            for (var j = 0; j < 关键词.length; j++) {
                if (模糊匹配) {
                    // 使用模糊匹配
                    var 相似度 = 计算字符串相似度(文本, 关键词[j]);
                    if (相似度 >= 模糊阈值) {
                        是否匹配 = true;
                        break;
                    }
                } else {
                    // 使用精确匹配
                    if (文本.indexOf(关键词[j]) !== -1) {
                        是否匹配 = true;
                        break;
                    }
                }
            }

            if (!是否匹配) {
                continue;
            }

            // 检查匹配关键词
            if (匹配关键词.length > 0) {
                for (var j = 0; j < 匹配关键词.length; j++) {
                    if (模糊匹配) {
                        // 使用模糊匹配
                        var 相似度 = 计算字符串相似度(文本, 匹配关键词[j]);
                        if (相似度 >= 模糊阈值) {
                            匹配关键词数++;
                        }
                    } else {
                        // 使用精确匹配
                        if (文本.indexOf(匹配关键词[j]) !== -1) {
                            匹配关键词数++;
                        }
                    }
                }

                // 检查是否满足匹配数量要求
                if (匹配数量 > 0 && 匹配关键词数 < 匹配数量) {
                    continue;
                }
            }

            // 检查排除关键词
            var 是否排除 = false;
            for (var j = 0; j < 排除关键词.length; j++) {
                if (模糊匹配) {
                    // 使用模糊匹配
                    var 相似度 = 计算字符串相似度(文本, 排除关键词[j]);
                    if (相似度 >= 模糊阈值) {
                        是否排除 = true;
                        break;
                    }
                } else {
                    // 使用精确匹配
                    if (文本.indexOf(排除关键词[j]) !== -1) {
                        是否排除 = true;
                        break;
                    }
                }
            }

            if (是否排除) {
                continue;
            }

            // 找到匹配项
            console.log(`找到匹配文字: "${文本}"`);
            return 项目;
        }

        console.log("未找到匹配文字");
        return null;
    } catch (error) {
        console.error(`查找文字出错: ${error.message}`);
        return null;
    }
}

/**
 * 计算两个字符串的相似度
 * @param {string} str1 - 字符串1
 * @param {string} str2 - 字符串2
 * @returns {number} 相似度(0-1)
 */
function 计算字符串相似度(str1, str2) {
    if (!str1 || !str2) {
        return 0;
    }

    // 简单实现：计算较长字符串中包含较短字符串的字符数量比例
    var 短字符串 = str1.length < str2.length ? str1 : str2;
    var 长字符串 = str1.length >= str2.length ? str1 : str2;

    var 匹配字符数 = 0;
    for (var i = 0; i < 短字符串.length; i++) {
        if (长字符串.indexOf(短字符串[i]) !== -1) {
            匹配字符数++;
        }
    }

    return 匹配字符数 / 短字符串.length;
}

/**
 * 清除识别缓存
 */
function 清除识别缓存() {
    识别缓存 = {};
    console.log("OCR识别缓存已清除");
}

/**
 * 设置最大缓存数量
 * @param {number} 数量 - 最大缓存数量
 */
function 设置最大缓存数量(数量) {
    if (typeof 数量 !== 'number' || 数量 < 1) {
        console.error("最大缓存数量必须是正整数");
        return;
    }

    最大缓存数量 = 数量;
    console.log(`OCR最大缓存数量已设置为 ${数量}`);

    // 如果当前缓存超过新的最大数量，清理多余的缓存
    var 缓存键列表 = Object.keys(识别缓存);
    if (缓存键列表.length > 最大缓存数量) {
        var 需要删除的数量 = 缓存键列表.length - 最大缓存数量;
        for (var i = 0; i < 需要删除的数量; i++) {
            delete 识别缓存[缓存键列表[i]];
        }
    }
}

/**
 * 初始化OCR引擎
 * 确保OCR引擎正确初始化并可用
 * @returns {boolean} 是否初始化成功
 */
function 初始化OCR引擎() {
    try {
        // 检查是否在AutoJS环境中
        if (!isAutoJsEnv) {
            console.log("非AutoJS环境，使用模拟OCR引擎");
            return true;
        }

        // 检查可用的OCR引擎
        if (typeof mlkit !== 'undefined' && mlkit.ocr) {
            console.log("已检测到MLKit OCR引擎");
            当前引擎 = OCR引擎类型.内置;
            return true;
        } else if (typeof paddle !== 'undefined' && paddle.ocr) {
            console.log("已检测到Paddle OCR引擎");
            当前引擎 = OCR引擎类型.内置;
            return true;
        } else if (typeof $ocr !== 'undefined') {
            console.log("已检测到AutoJS内置OCR引擎");
            当前引擎 = OCR引擎类型.内置;
            return true;
        }

        console.error("未检测到可用的OCR引擎");
        return false;
    } catch (error) {
        console.error(`初始化OCR引擎出错: ${error.message}`);
        return false;
    }
}

/**
 * 调试显示所有文字
 * 专门用于调试，显示屏幕上所有识别到的文字
 * @returns {Array} 识别到的所有文字数组
 */
function 调试显示所有文字() {
    console.log("[调试增强] 开始调试显示所有文字...");
    let img = 获取屏幕截图();
    if (!img) {
        console.log("[调试增强] 截图失败");
        return [];
    }

    try {
        // 使用$ocr.detect直接获取所有文字及位置信息
        let allTextWithPos = $ocr.detect(img);
        if (!allTextWithPos || !Array.isArray(allTextWithPos) || allTextWithPos.length === 0) {
            console.log("[调试增强] OCR识别失败或返回空结果");
            return [];
        }

        console.log(`[调试增强] ===== 屏幕上共识别到 ${allTextWithPos.length} 个文字项 =====`);

        // 按位置从上到下排序
        allTextWithPos.sort((a, b) => {
            return a.bounds.top - b.bounds.top;
        });

        // 显示所有识别到的文字的原始内容（不修改任何内容）
        console.log("[调试增强] ===== 识别到的所有文字原始内容 =====");
        console.log(JSON.stringify(allTextWithPos.map(item => ({
            text: item.label,
            bounds: item.bounds
        }))));

        // 显示所有文字项及其位置
        for (let i = 0; i < allTextWithPos.length; i++) {
            let item = allTextWithPos[i];
            let left = item.bounds.left;
            let top = item.bounds.top;
            let right = item.bounds.right;
            let bottom = item.bounds.bottom;
            let centerX = (left + right) / 2;
            let centerY = (top + bottom) / 2;

            console.log(`[调试增强] 文字项 ${i + 1}: "${item.label}" - 位置: [${Math.round(left)},${Math.round(top)} - ${Math.round(right)},${Math.round(bottom)}], 中心点: (${Math.round(centerX)},${Math.round(centerY)})`);
        }

        // 专门检查"设置"相关文字
        console.log("[调试增强] ===== 检查'设置'相关文字 =====");
        let 设置文字 = allTextWithPos.filter(item =>
            item.label === "设置" ||
            item.label.includes("设置") ||
            item.label.includes("设 置") ||
            item.label.toLowerCase().includes("settings")
        );

        if (设置文字.length > 0) {
            console.log(`[调试增强] 发现${设置文字.length}个'设置'相关文字:`);
            设置文字.forEach((item, idx) => {
                console.log(`[调试增强]   ${idx + 1}. "${item.label}" - 坐标: [${Math.round(item.bounds.left)},${Math.round(item.bounds.top)}]-[${Math.round(item.bounds.right)},${Math.round(item.bounds.bottom)}]`);
                // 显示原始label和字节表示形式，用于检查是否有隐藏字符
                let bytes = Array.from(item.label).map(char => char.charCodeAt(0).toString(16).padStart(4, '0')).join(' ');
                console.log(`[调试增强]      原始文本: "${item.label}", 字节表示: ${bytes}`);
            });
        } else {
            console.log("[调试增强] 未发现任何'设置'相关文字");
        }

        // 简洁显示所有文字，便于复制查看
        console.log("[调试增强] ===== 所有识别到的文字内容 =====");
        console.log(allTextWithPos.map(item => `"${item.label}"`).join(", "));

        console.log("[调试增强] ===== 调试结束 =====");

        return allTextWithPos.map(item => item.label);
    } catch (e) {
        console.error("[调试增强] OCR调试识别出错:", e);
        return [];
    } finally {
        if (img) {
            try {
                img.recycle();
            } catch (e) {
                console.error("[调试增强] 释放截图资源出错:", e);
            }
        }
    }
}

/**
 * 匹配文本与关键词，支持通配符 ? 和 *
 * ? 匹配单个字符
 * * 匹配任意数量的字符
 * @param {string} 文本 - 要匹配的文本
 * @param {string} 关键词 - 包含通配符的关键词
 * @returns {boolean} 是否匹配成功
 */
function 匹配文本与关键词(文本, 关键词) {
    if (!文本 || !关键词) return false;

    // console.log(`[通配符匹配] 尝试匹配: 文本="${文本}", 关键词="${关键词}"`);

    // 处理多个关键词的情况（以逗号或竖线分隔）
    if (关键词.indexOf(',') !== -1 || 关键词.indexOf('|') !== -1) {
        let 分隔符 = 关键词.indexOf(',') !== -1 ? ',' : '|';
        let 关键词数组 = 关键词.split(分隔符);

        // console.log(`[通配符匹配] 检测到多个关键词: [${关键词数组.join(', ')}]，使用分隔符: "${分隔符}"`);

        // 任何一个关键词匹配成功即返回true
        for (let 单个关键词 of 关键词数组) {
            if (匹配单个关键词(文本, 单个关键词.trim())) {
                // console.log(`[通配符匹配] 多关键词中的"${单个关键词.trim()}"匹配成功`);
                return true;
            }
        }

        // console.log(`[通配符匹配] 所有关键词均匹配失败`);
        return false;
    }

    // 单个关键词匹配
    return 匹配单个关键词(文本, 关键词);
}

/**
 * 匹配单个关键词
 * @param {string} 文本 - 要匹配的文本
 * @param {string} 关键词 - 单个关键词
 * @returns {boolean} 是否匹配成功
 */
function 匹配单个关键词(文本, 关键词) {
    // 如果关键词不包含通配符，使用严格全等匹配
    if (关键词.indexOf('?') === -1 && 关键词.indexOf('*') === -1) {
        return 文本 === 关键词;
    }

    // 特殊处理月日匹配和程序匹配
    if (关键词 === "*月*日*") {
        // console.log(`[通配符匹配] 检测到月日特殊匹配模式`);
        let 特殊处理 = 文本.indexOf("月") !== -1 && 文本.indexOf("日") !== -1;
        // console.log(`[通配符匹配] 月日特殊匹配结果: ${特殊处理}`);
        if (特殊处理) return true;
    } else if (关键词 === "*个程序*") {
        // console.log(`[通配符匹配] 检测到程序特殊匹配模式`);
        let 特殊处理 = 文本.indexOf("个程序") !== -1;
        // console.log(`[通配符匹配] 程序特殊匹配结果: ${特殊处理}`);
        if (特殊处理) return true;

        // 如果特殊处理失败，继续使用正则表达式匹配
        // 但不再匹配单独的"程序"，只匹配"个程序"
        let 正则字符串 = ".*个程序.*";
        // console.log(`[通配符匹配] 转换后的正则表达式: ${正则字符串}`);

        try {
            let 正则表达式 = new RegExp(正则字符串);
            let 匹配结果 = 正则表达式.test(文本);
            // console.log(`[通配符匹配] 正则匹配结果: ${匹配结果}`);
            return 匹配结果;
        } catch (e) {
            console.error(`[通配符匹配] 创建正则表达式失败: ${e.message}`);
            return false;
        }
    }

    // 将通配符关键词转换为正则表达式
    let 正则字符串 = 关键词
        .replace(/\./g, '\\.')   // 转义点号
        .replace(/\(/g, '\\(')   // 转义左括号
        .replace(/\)/g, '\\)')   // 转义右括号
        .replace(/\[/g, '\\[')   // 转义左方括号
        .replace(/\]/g, '\\]')   // 转义右方括号
        .replace(/\{/g, '\\{')   // 转义左花括号
        .replace(/\}/g, '\\}')   // 转义右花括号
        .replace(/\+/g, '\\+')   // 转义加号
        .replace(/\^/g, '\\^')   // 转义脱字符
        .replace(/\$/g, '\\$')   // 转义美元符号
        .replace(/\|/g, '\\|')   // 转义竖线
        .replace(/\?/g, '.')     // 问号转换为匹配任意单个字符
        .replace(/\*/g, '.*');   // 星号转换为匹配任意数量的字符

    try {
        // 创建正则表达式对象，不需要完全匹配，只需要包含匹配即可
        // console.log(`[通配符匹配] 转换后的正则表达式: ${正则字符串}`);

        let 正则表达式 = new RegExp(正则字符串);
        let 匹配结果 = 正则表达式.test(文本);

        // console.log(`[通配符匹配] 正则匹配结果: ${匹配结果}`);
        return 匹配结果;
    } catch (e) {
        // 如果正则表达式创建失败，回退到简单匹配
        console.error(`[通配符匹配] 创建正则表达式失败: ${e.message}, 关键词: ${关键词}`);
        let 简单匹配结果 = 文本 === 关键词 || 文本.indexOf(关键词) !== -1;
        // console.log(`[通配符匹配] 回退到简单匹配，结果: ${简单匹配结果}`);
        return 简单匹配结果;
    }
}

/**
 * 输出匹配失败的详细信息
 * @param {string} 文本 - 要匹配的文本
 * @param {string} 关键词 - 匹配的关键词
 */
function 输出匹配失败详情(文本, 关键词) {
    // console.log(`[通配符匹配] 匹配失败详情分析:`);
    // console.log(`[通配符匹配] - 文本长度: ${文本.length}`);
    // console.log(`[通配符匹配] - 关键词长度: ${关键词.length}`);

    // 检查文本中是否包含关键词的非通配符部分
    let 非通配符部分 = 关键词.replace(/[\*\?]/g, '');
    if (非通配符部分) {
        // console.log(`[通配符匹配] - 关键词非通配符部分: "${非通配符部分}"`);
        // console.log(`[通配符匹配] - 文本中包含非通配符部分: ${文本.includes(非通配符部分)}`);
    }

    // 尝试将文本和关键词转换为字符码点，检查是否有不可见字符
    // console.log(`[通配符匹配] - 文本字符码: ${Array.from(文本).map(c => c.charCodeAt(0).toString(16)).join(' ')}`);
    // console.log(`[通配符匹配] - 关键词字符码: ${Array.from(关键词).map(c => c.charCodeAt(0).toString(16)).join(' ')}`);
}

// 最后导出模块
module.exports = {
    OCR引擎类型,
    设置OCR引擎,
    识别文字,
    查找文字,
    清除识别缓存,
    设置最大缓存数量,
    初始化OCR引擎,
    获取屏幕截图,
    获取屏幕文字信息,
    点击指定文字坐标,
    判断屏幕包含文字,
    查找文字位置,
    释放OCR资源,
    获取所有文字,
    初始化OCR,
    是需要更新的操作类型,
    调试显示所有文字,
    匹配文本与关键词,
    点击指定关键词,
    长按指定关键词,
    申请截图权限_增强,
    检查截图权限
};

// 将主要函数直接添加到全局，确保可以直接调用
console.log("=== 添加OCR函数到全局作用域 ===");
global.获取屏幕文字信息 = 获取屏幕文字信息;
global.点击指定文字坐标 = 点击指定文字坐标;
global.判断屏幕包含文字 = 判断屏幕包含文字;
global.查找文字位置 = 查找文字位置;
global.调试显示所有文字 = 调试显示所有文字;
global.获取所有文字 = 获取所有文字;
global.OCR初始化 = 初始化OCR;
global.申请截图权限_增强 = 申请截图权限_增强;
global.申请截图权限 = function () {
    console.log("ROOT模式下，申请截图权限函数被调用，直接返回true");
    return true;
};
global.检查截图权限 = 检查截图权限;
global.直接点击指定文字 = function (关键词, 序号 = 1, 排除关键词 = "") {
    console.log(`直接点击文字: ${关键词}, 序号: ${序号}, 排除: ${排除关键词}`);
    return 点击指定文字坐标(null, 关键词, 序号, 排除关键词);
};
global.带偏移点击文字 = function (关键词, 偏移X = 0, 偏移Y = 0, 序号 = 1, 排除关键词 = "") {
    console.log(`带偏移点击文字: ${关键词}, 偏移: (${偏移X}, ${偏移Y}), 序号: ${序号}, 排除: ${排除关键词}`);
    return 点击指定文字坐标(null, 关键词, 序号, 排除关键词, 偏移X, 偏移Y);
};
global.带偏移点击结果 = function (识别结果, 偏移X = 0, 偏移Y = 0, 序号 = 1) {
    if (!识别结果 || 识别结果.length === 0) {
        console.log("带偏移点击结果: 结果为空，无法点击");
        return false;
    }
    console.log(`带偏移点击结果: 结果长度=${识别结果.length}, 偏移: (${偏移X}, ${偏移Y}), 序号: ${序号}`);
    return 点击指定文字坐标(识别结果, "", 序号, "", 偏移X, 偏移Y);
};

/**
 * 按关键词点击识别结果中的目标项
 * @param {Array} 识别结果 - OCR识别结果数组
 * @param {string} 关键词 - 要点击的目标文字（支持通配符）
 * @param {string} 排除文字 - 要排除的文字
 * @param {number} 偏移X - X轴偏移
 * @param {number} 偏移Y - Y轴偏移
 * @param {number} 点击次数 - 点击次数，默认为1次
 * @param {number} 点击间隔 - 多次点击时的间隔时间(毫秒)，默认为50毫秒
 * @returns {boolean} 是否点击成功
 */
function 点击指定关键词(识别结果, 关键词, 排除文字 = "", 偏移X = 0, 偏移Y = 0, 点击次数 = 1, 点击间隔 = 50) {
    if (!识别结果 || !关键词) return false;
    关键词 = String(关键词 || "");
    排除文字 = String(排除文字 || "");
    偏移X = Number(偏移X) || 0;
    偏移Y = Number(偏移Y) || 0;
    点击次数 = Number(点击次数) || 1;
    点击间隔 = Number(点击间隔) || 50;

    for (let i = 0; i < 识别结果.length; i++) {
        let item = 识别结果[i];
        if (匹配文本与关键词(item.text, 关键词)) {
            if (排除文字 && 匹配文本与关键词(item.text, 排除文字)) continue;
            let centerX = item.position.centerX + 偏移X;
            let centerY = item.position.centerY + 偏移Y;
            console.log(`[关键词点击] 点击: "${item.text}" 关键词: "${关键词}" 位置: (${centerX},${centerY}) 次数: ${点击次数}`);
            try {
                // 执行多次点击
                if (点击次数 > 1) {
                    // 使用连续点击函数
                    DeviceOperation.连续点击(centerX, centerY, 点击次数, 点击间隔);
                } else {
                    // 单次点击
                    DeviceOperation.点击(centerX, centerY);
                }
                //DeviceOperation.sleep(1000);
                return true;
            } catch (e) {
                console.error(`[关键词点击] 点击失败: ${e.message}`);
                return false;
            }
        }
    }
    console.error(`[关键词点击] 未找到匹配关键词: "${关键词}"`);
    return false;
}

// 导出到全局
global.点击指定关键词 = 点击指定关键词;

/**
 * 按关键词长按识别结果中的目标项
 * @param {Array} 识别结果 - OCR识别结果数组
 * @param {string} 关键词 - 要长按的目标文字（支持通配符）
 * @param {string} 排除文字 - 要排除的文字
 * @param {number} 偏移X - X轴偏移
 * @param {number} 偏移Y - Y轴偏移
 * @param {number} 时长 - 长按时长（毫秒，默认1200）
 * @returns {boolean} 是否长按成功
 */
function 长按指定关键词(识别结果, 关键词, 排除文字 = "", 偏移X = 0, 偏移Y = 0, 时长 = 1200) {
    if (!识别结果 || !关键词) return false;
    关键词 = String(关键词 || "");
    排除文字 = String(排除文字 || "");
    偏移X = Number(偏移X) || 0;
    偏移Y = Number(偏移Y) || 0;
    时长 = Number(时长) || 1200;
    for (let i = 0; i < 识别结果.length; i++) {
        let item = 识别结果[i];
        if (匹配文本与关键词(item.text, 关键词)) {
            if (排除文字 && 匹配文本与关键词(item.text, 排除文字)) continue;
            let centerX = item.position.centerX + 偏移X;
            let centerY = item.position.centerY + 偏移Y;
            console.log(`[关键词长按] 长按: "${item.text}" 关键词: "${关键词}" 位置: (${centerX},${centerY}) 时长: ${时长}`);
            try {
                //press(x, y, duration)                
                DeviceOperation.长按(centerX, centerY, 时长);
                DeviceOperation.sleep(1000);
                return true;
            } catch (e) {
                console.error(`[关键词长按] 长按失败: ${e.message}`);
                return false;
            }
        }
    }
    console.error(`[关键词长按] 未找到匹配关键词: "${关键词}"`);
    return false;
}
// ... existing code ...
global.长按指定关键词 = 长按指定关键词;

// ROOT模式专用权限函数
function 申请截图权限_增强(maxRetry = 5) {
    console.log("申请截图权限（ROOT模式专用）...");

    // 检查当前操作模式
    let 当前操作模式 = null;
    if (typeof DeviceOperation !== 'undefined' && typeof DeviceOperation.获取当前模式 === 'function') {
        当前操作模式 = DeviceOperation.获取当前模式();
    }

    // ROOT模式下直接返回成功，不需要申请权限
    if (当前操作模式 === 操作模式.ROOT || 当前操作模式 === 3) {
        console.log("ROOT模式下无需申请截图权限，直接返回成功");
        return true;
    }

    // 非ROOT模式不支持
    console.error("脚本已配置为仅支持ROOT模式");
    return false;
}



// ROOT模式专用检查截图权限函数
function 检查截图权限() {
    try {
        // 检查当前操作模式
        let 当前操作模式 = null;
        if (typeof DeviceOperation !== 'undefined' && typeof DeviceOperation.获取当前模式 === 'function') {
            当前操作模式 = DeviceOperation.获取当前模式();
        }

        // ROOT模式下直接返回true
        if (当前操作模式 === 操作模式.ROOT || 当前操作模式 === 3) {
            console.log("ROOT模式下，跳过截图权限检查，直接返回true");
            return true;
        }

        // 非ROOT模式不支持
        console.error("脚本已配置为仅支持ROOT模式");
        return false;
    } catch (e) {
        console.error("检查截图权限出错:", e);
        return false;
    }
}

// 导出检查截图权限函数
if (typeof module !== 'undefined' && module.exports) {
    module.exports.检查截图权限 = 检查截图权限;
}
