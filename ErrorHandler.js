/**
 * 错误处理模块 - 处理和恢复错误
 * 作者: Claude
 * 日期: 2025-07-01
 */

// 错误记录
var 错误记录 = [];
var 连续错误计数 = 0;
var 最大连续错误 = 5;
var 最大错误记录数 = 100;
var 错误恢复策略 = "返回";  // 返回, 返回到桌面, 重启应用

/**
 * 设置错误处理配置
 * @param {Object} 配置 - 错误处理配置
 * @param {number} [配置.最大连续错误=5] - 最大连续错误次数
 * @param {number} [配置.最大错误记录数=100] - 最大错误记录数
 * @param {string} [配置.错误恢复策略="返回"] - 错误恢复策略
 */
function 设置错误处理配置(配置 = {}) {
    if (配置.最大连续错误 !== undefined) {
        最大连续错误 = 配置.最大连续错误;
    }
    
    if (配置.最大错误记录数 !== undefined) {
        设置最大错误记录数(配置.最大错误记录数);
    }
    
    if (配置.错误恢复策略 !== undefined) {
        错误恢复策略 = 配置.错误恢复策略;
    }
    
    console.log("错误处理配置已更新: 最大连续错误=" + 最大连续错误 + ", 最大错误记录数=" + 最大错误记录数 + ", 错误恢复策略=" + 错误恢复策略);
}

/**
 * 设置最大错误记录数
 * @param {number} 数量 - 最大错误记录数
 */
function 设置最大错误记录数(数量) {
    if (typeof 数量 !== 'number' || 数量 < 1) {
        console.error("最大错误记录数必须是正整数");
        return;
    }
    
    最大错误记录数 = 数量;
    console.log("最大错误记录数已设置为 " + 数量);
    
    // 如果当前记录超过新的最大数量，清理多余的记录
    if (错误记录.length > 最大错误记录数) {
        错误记录 = 错误记录.slice(-最大错误记录数);
        console.log("已清理多余的错误记录");
    }
}

/**
 * 记录错误
 * @param {string} 错误消息 - 错误消息
 * @param {string} 步骤描述 - 步骤描述
 * @param {string} 步骤类型 - 步骤类型
 */
function 记录错误(错误消息, 步骤描述, 步骤类型) {
    // 增加连续错误计数
    连续错误计数++;
    
    // 创建错误记录
    var 记录 = {
        时间: new Date().toISOString(),
        错误消息: 错误消息,
        步骤描述: 步骤描述,
        步骤类型: 步骤类型,
        连续错误计数: 连续错误计数
    };
    
    // 添加到记录列表
    错误记录.push(记录);
    
    // 如果超过最大记录数，删除最早的记录
    if (错误记录.length > 最大错误记录数) {
        错误记录.shift();
    }
    
    console.error("错误记录 #" + 连续错误计数 + ": " + 错误消息 + " (步骤: " + 步骤描述 + ", 类型: " + 步骤类型 + ")");
}

/**
 * 重置连续错误计数
 */
function 重置连续错误计数() {
    if (连续错误计数 > 0) {
        console.log("重置连续错误计数，之前为: " + 连续错误计数);
        连续错误计数 = 0;
    }
}

/**
 * 获取错误记录
 * @param {number} [数量=10] - 获取的记录数量
 * @returns {Array} 错误记录数组
 */
function 获取错误记录(数量 = 10) {
    var 返回数量 = Math.min(数量, 错误记录.length);
    return 错误记录.slice(-返回数量);
}

/**
 * 处理错误
 * @param {string} 错误消息 - 错误消息
 * @param {string} 步骤描述 - 步骤描述
 * @param {string} 步骤类型 - 步骤类型
 * @param {Function} 返回函数 - 返回操作函数
 * @param {Function} 返回到桌面函数 - 返回到桌面函数
 * @returns {Promise<boolean>} 是否处理成功
 */
function 处理错误(错误消息, 步骤描述, 步骤类型, 返回函数, 返回到桌面函数) {
    return new Promise(function(resolve) {
        try {
            // 检查连续错误是否超过阈值
            if (连续错误计数 >= 最大连续错误) {
                console.error("连续错误次数 (" + 连续错误计数 + ") 超过最大值 (" + 最大连续错误 + ")，执行恢复操作");
                
                // 根据恢复策略执行不同的操作
                switch (错误恢复策略) {
                    case "返回":
                        console.log("执行返回操作...");
                        返回函数().then(function() {
                            resolve(true);
                        }).catch(function() {
                            console.error("返回操作失败");
                            resolve(false);
                        });
                        break;
                        
                    case "返回到桌面":
                        console.log("执行返回到桌面操作...");
                        返回到桌面函数().then(function() {
                            resolve(true);
                        }).catch(function() {
                            console.error("返回到桌面操作失败");
                            resolve(false);
                        });
                        break;
                        
                    case "重启应用":
                        console.log("执行重启应用操作...");
                        // 先返回到桌面
                        返回到桌面函数().then(function() {
                            // 重启应用的逻辑需要在这里实现
                            // 可以使用app.launchApp或其他方式
                            console.log("返回到桌面成功，应重启应用");
                            resolve(true);
                        }).catch(function() {
                            console.error("返回到桌面操作失败");
                            resolve(false);
                        });
                        break;
                        
                    default:
                        console.error("未知的错误恢复策略: " + 错误恢复策略);
                        resolve(false);
                        break;
                }
            } else {
                // 连续错误未超过阈值，不执行恢复操作
                resolve(true);
            }
        } catch (error) {
            console.error("处理错误过程出错: " + error.message);
            resolve(false);
        }
    });
}

/**
 * 清除错误记录
 */
function 清除错误记录() {
    错误记录 = [];
    连续错误计数 = 0;
    console.log("错误记录已清除");
}

// 导出模块
// 在AutoJS环境中使用全局变量导出
if (typeof module !== 'undefined' && module.exports) {
    // Node.js环境
    module.exports = {
        设置错误处理配置,
        设置最大错误记录数,
        记录错误,
        重置连续错误计数,
        获取错误记录,
        处理错误,
        清除错误记录
    };
} else {
    // AutoJS环境
    global.ErrorHandler = {
        设置错误处理配置,
        设置最大错误记录数,
        记录错误,
        重置连续错误计数,
        获取错误记录,
        处理错误,
        清除错误记录
    };
} 