/**
 * 小红书API操作模块
 * 包含API接口调用和互动元素获取功能
 * 
 * 功能列表：
 * 1. 设备注册API
 * 2. 获取链接API
 * 3. 更新操作状态API
 * 4. 设备令牌管理
 * 5. 操作信息管理
 * 6. 互动元素获取
 * 
 * 作者: Claude
 * 日期: 2024-07-12
 */

// 引入DeviceOperation模块，用于设备操作
var DeviceOperation = require('./DeviceOperation.js');

// 引入OCR模块
var OCR = require('./ocr.js');

// 在文件开头添加一个标志，表示是否是首次运行
let 是否首次运行 = true;

/**
 * API配置
 * 根据API调用说明文档配置API路径
 */
const API配置 = {
    基本URL: "http://xhss.ke999.cn",  // 移除"/api"前缀
    用户名: "admin", // 请在此处填写您的用户名
    设备令牌: "", // 如果为空，将自动生成
    设备名称: typeof device !== 'undefined' ? (device.brand + " " + device.model) : "未知设备" // 自动获取设备名称
};

// 检查AutoJS环境
if (typeof auto === 'undefined') {
    console.log("警告：未在AutoJS环境中运行，部分功能可能不可用");
    // 创建模拟device对象，避免报错
    if (typeof device === 'undefined') {
        global.device = {
            width: 1080,
            height: 1920,
            brand: "模拟",
            model: "设备",
            release: "未知"
        };
    }
}

console.log("小红书API模块已加载");

/**
 * 生成随机设备令牌
 * 
 * @returns {string} - 随机生成的设备令牌
 */
function 生成设备令牌() {
    // 使用与网页测试一致的格式：device_时间戳_随机字符串
    const 时间戳 = Date.now();
    let 随机字符串 = "";
    const 可能字符 = "abcdefghijklmnopqrstuvwxyz0123456789";

    // 生成8位随机字符串
    for (let i = 0; i < 8; i++) {
        随机字符串 += 可能字符.charAt(Math.floor(Math.random() * 可能字符.length));
    }

    // 格式: device_时间戳_随机字符串
    return `device_${时间戳}_${随机字符串}`;
}

/**
 * 获取或初始化设备令牌
 * 
 * @returns {string} - 设备令牌
 */
function 获取设备令牌() {
    // 如果已经设置了设备令牌，直接返回
    if (API配置.设备令牌) {
        return API配置.设备令牌;
    }

    // 尝试从存储中读取
    let 存储的令牌 = storages.create("小红书操作").get("设备令牌");
    if (存储的令牌) {
        console.log("使用存储的设备令牌: " + 存储的令牌);
        API配置.设备令牌 = 存储的令牌;
        return 存储的令牌;
    }

    // 如果是首次运行且没有存储的令牌，则生成新的设备令牌
    if (是否首次运行) {
        console.log("首次运行，生成新的设备令牌");
        let 新令牌 = 生成设备令牌();
        API配置.设备令牌 = 新令牌;

        // 保存到存储
        storages.create("小红书操作").put("设备令牌", 新令牌);

        // 标记为非首次运行
        是否首次运行 = false;

        return 新令牌;
    }

    // 非首次运行但没有令牌，使用固定令牌
    let 固定令牌 = device.brand + device.model + "_fixed_token_" + device.getAndroidId().substring(0, 8);
    console.log("使用固定设备令牌: " + 固定令牌);
    API配置.设备令牌 = 固定令牌;

    // 保存到存储
    storages.create("小红书操作").put("设备令牌", 固定令牌);

    return 固定令牌;
}

/**
 * 更新设备令牌（当达到每日限制时调用）
 * 
 * @returns {string} - 新的设备令牌
 */
function 更新设备令牌() {
    let 新令牌 = 生成设备令牌();
    API配置.设备令牌 = 新令牌;

    // 保存到存储
    storages.create("小红书操作").put("设备令牌", 新令牌);

    console.log("已更新设备令牌: " + 新令牌);
    return 新令牌;
}

/**
 * 发送HTTP请求
 * 
 * @param {string} 方法 - 请求方法，"GET"或"POST"
 * @param {string} 路径 - API路径
 * @param {Object} 数据 - 请求数据
 * @returns {Object} - 响应数据
 */
function 发送请求(方法, 路径, 数据) {
    let url = API配置.基本URL + 路径;

    console.log(`发送${方法}请求到: ${url}`);
    console.log(`请求数据: ${JSON.stringify(数据)}`);

    try {
        let response;

        if (方法 === "GET" && 数据) {
            // 构建查询字符串
            let 查询参数 = [];
            for (let key in 数据) {
                查询参数.push(encodeURIComponent(key) + "=" + encodeURIComponent(数据[key]));
            }
            url += "?" + 查询参数.join("&");
            console.log(`完整GET请求URL: ${url}`);
            response = http.get(url);
        } else if (方法 === "POST") {
            // 发送POST请求，不设置contentType
            response = http.post(url, 数据);
        } else {
            response = http.get(url);
        }

        // 记录响应状态码
        console.log(`响应状态码: ${response.statusCode}`);

        if (response.statusCode >= 200 && response.statusCode < 300) {
            let 响应内容 = response.body.json();
            console.log(`响应内容: ${JSON.stringify(响应内容)}`);
            return 响应内容;
        } else {
            // 详细记录错误信息
            let 错误信息 = `请求失败，状态码: ${response.statusCode}`;
            try {
                let 错误响应 = response.body.string();
                console.error(错误信息);
                console.error(`错误响应: ${错误响应}`);
                return {
                    success: false,
                    message: 错误信息,
                    error_details: 错误响应
                };
            } catch (e) {
                console.error(`${错误信息}, 无法解析响应内容`);
                return { success: false, message: 错误信息 };
            }
        }
    } catch (e) {
        console.error(`发送请求出错: ${e.message}`);
        console.error(`错误堆栈: ${e.stack}`);
        return {
            success: false,
            message: `发送请求出错: ${e.message}`,
            error_stack: e.stack
        };
    }
}

/**
 * 设备注册API
 * 
 * @param {string} 设备类型 - 可选，设备类型，默认为'mobile'
 * @param {boolean} 是否临时设备 - 可选，是否为临时设备，默认为false
 * @returns {Object} - 注册结果
 */
function 设备注册(设备类型 = 'mobile', 是否临时设备 = false) {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;

    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }

    let 数据 = {
        username: 用户名,
        device_token: 设备令牌,
        device_name: API配置.设备名称,
        device_type: 设备类型,
        is_temp: 是否临时设备
    };

    console.log("发送设备注册请求: " + JSON.stringify(数据));

    try {
        // 构建URL - 使用与网页测试一致的路径
        let url = API配置.基本URL + "/device/register";
        console.log(`注册路径: ${url}`);

        // 使用POST请求，不设置contentType
        let 响应 = http.post(url, 数据);

        console.log(`注册响应状态码: ${响应.statusCode}`);

        if (响应.statusCode >= 200 && 响应.statusCode < 300) {
            let 结果 = 响应.body.json();
            console.log("设备注册结果: " + JSON.stringify(结果));

            // 保存操作次数信息和配置信息
            if (结果.success && 结果.data) {
                // 保存操作信息
                let 操作信息 = {
                    操作次数: 结果.data.operation_count || 0,
                    最大操作次数: 结果.data.max_operations || 0,
                    剩余操作次数: 结果.data.remaining_operations || 0
                };
                storages.create("小红书操作").put("操作信息", 操作信息);

                // 检查是否已达到每日操作上限
                if (操作信息.剩余操作次数 <= 0) {
                    console.log("当前设备今日操作次数已达上限，无法继续操作");
                    toast("今日操作次数已达上限，即将退出本次注册");
                    sleep(3000);
                    return { success: false, message: "今日操作次数已达上限", limitReached: true };
                }

                // 保存设备ID
                if (结果.data.device_id) {
                    storages.create("小红书操作").put("设备ID", 结果.data.device_id);
                }

                // 保存配置信息
                if (结果.data.configs && Array.isArray(结果.data.configs) && 结果.data.configs.length > 0) {
                    // 提取所有配置项
                    let 所有配置 = {};

                    // 遍历所有配置
                    for (let i = 0; i < 结果.data.configs.length; i++) {
                        let 配置项 = 结果.data.configs[i];

                        // 如果配置有name和config属性
                        if (配置项.name && 配置项.config) {
                            // 将配置添加到所有配置对象
                            所有配置[配置项.name] = 配置项.config;

                            // 如果是默认配置，单独保存
                            if (配置项.is_default === 1) {
                                for (let 键 in 配置项.config) {
                                    console.log(`已保存默认配置: ${键}`);
                                    storages.create("小红书操作").put(键, 配置项.config[键]);

                                    // 特别处理"操作几次恢复出厂设置"，设置为全局变量
                                    if (键 === "操作几次恢复出厂设置") {
                                        // 设置为全局变量，方便其他模块访问
                                        global.操作几次恢复出厂设置 = 配置项.config[键];
                                        console.log(`已设置全局变量: 操作几次恢复出厂设置 = ${global.操作几次恢复出厂设置}`);
                                    }
                                }
                            }
                        }
                    }

                    // 保存所有配置
                    storages.create("小红书操作").put("所有配置", 所有配置);

                    // 寻找默认配置
                    let 默认配置 = 结果.data.configs.find(配置 => 配置.is_default === 1);

                    // 如果没有默认配置，使用第一个配置
                    if (!默认配置 && 结果.data.configs.length > 0) {
                        默认配置 = 结果.data.configs[0];
                    }

                    // 保存默认配置
                    if (默认配置) {
                        storages.create("小红书操作").put("当前配置", 默认配置);

                        // 如果默认配置中有"操作几次恢复出厂设置"，设置为全局变量
                        if (默认配置.config && 默认配置.config.操作几次恢复出厂设置 !== undefined) {
                            global.操作几次恢复出厂设置 = 默认配置.config.操作几次恢复出厂设置;
                            console.log(`从默认配置设置全局变量: 操作几次恢复出厂设置 = ${global.操作几次恢复出厂设置}`);
                        }
                    }
                }

                // 输出设备令牌，便于调试
                console.log("设备注册成功，当前设备令牌: " + 设备令牌);
                console.log("当前操作信息: " + JSON.stringify(操作信息));
            }

            return 结果;
        } else {
            // 请求失败
            let 错误信息 = `注册请求失败，状态码: ${响应.statusCode}`;
            try {
                let 错误响应 = 响应.body.string();
                console.error(错误信息);
                console.error(`错误响应: ${错误响应}`);
                return {
                    success: false,
                    message: 错误信息,
                    error_details: 错误响应
                };
            } catch (e) {
                console.error(`${错误信息}, 无法解析响应内容`);
                return { success: false, message: 错误信息 };
            }
        }
    } catch (e) {
        console.error(`发送注册请求出错: ${e.message}`);
        console.error(`错误堆栈: ${e.stack}`);
        return {
            success: false,
            message: `发送注册请求出错: ${e.message}`,
            error_stack: e.stack
        };
    }
}

/**
 * 获取下一个链接API
 * 
 * @returns {Object} - 链接数据
 */
function 获取下一个链接() {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;

    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }

    let 数据 = {
        username: 用户名,  // 调整参数顺序，与网页测试一致
        device_token: 设备令牌
    };

    let 结果 = 发送请求("GET", "/next-link", 数据);
    console.log("获取链接结果: " + JSON.stringify(结果));

    // 显示toast提示消息
    if (结果.toast && 结果.toast.message) {
        toast(结果.toast.message);
    } else if (结果.status === "no_links") {
        toast("当前没有可操作的链接，30秒后将重试");
    } else if (结果.status === "limit_reached") {
        toast("今日操作次数已达上限，即将退出脚本");
        console.log("达到每日操作上限，准备退出脚本");
        // 等待3秒，让用户看到提示
        sleep(3000);
        // 退出脚本
        exit();
    } else if (结果.success && 结果.status === "success") {
        toast("获取链接成功，正在处理...");
    }

    // 如果达到限制，更新设备令牌
    if (结果.status === "limit_reached") {
        console.log("已达到每日限制，更新设备令牌");
        更新设备令牌();
    }

    // 更新操作次数信息
    if (结果.success && 结果.data) {
        let 操作信息 = {
            操作次数: 结果.data.operation_count || 0,
            最大操作次数: 结果.data.max_operations || 0,
            剩余操作次数: (结果.data.max_operations || 0) - (结果.data.operation_count || 0)
        };
        storages.create("小红书操作").put("操作信息", 操作信息);

        // 保存当前链接ID，供更新状态使用
        if (结果.data.link_id) {
            storages.create("小红书操作").put("当前链接ID", 结果.data.link_id);
            console.log("保存链接ID: " + 结果.data.link_id);
        }
    }

    return 结果;
}

/**
 * 更新操作状态API
 * 
 * @param {number} 链接ID - 链接ID
 * @param {string} 状态 - 操作状态: success(成功)或failed(失败)
 * @param {string} 操作类型 - 操作类型: like(点赞)、collect(收藏)或both(两者)
 * @param {number} 操作前点赞数 - 操作前的点赞数量
 * @param {number} 操作前收藏数 - 操作前的收藏数量
 * @param {string} 错误信息 - 错误信息(仅当status=failed时需要)
 * @returns {Object} - 更新结果
 */
function 更新操作状态(链接ID, 状态, 操作类型, 操作前点赞数, 操作前收藏数, 错误信息) {
    let 设备令牌 = 获取设备令牌();
    let 用户名 = API配置.用户名;

    if (!用户名) {
        console.error("用户名未设置");
        return { success: false, message: "用户名未设置" };
    }

    let 数据 = {
        username: 用户名,  // 调整参数顺序，与网页测试一致
        device_token: 设备令牌,
        link_id: 链接ID,
        status: 状态,
        operation_type: 操作类型,
        before_like_count: 操作前点赞数,
        before_collect_count: 操作前收藏数
    };

    if (状态 === "failed" && 错误信息) {
        数据.error_message = 错误信息;
    }

    let 结果 = 发送请求("POST", "/update-status", 数据);
    console.log("更新状态结果: " + JSON.stringify(结果));

    // 更新操作次数信息
    if (结果.success && 结果.data) {
        let 操作信息 = {
            操作次数: 结果.data.operation_count || 0,
            最大操作次数: 结果.data.max_operations || 0,
            剩余操作次数: 结果.data.remaining_operations || 0
        };
        
        // 记录服务端返回的操作次数信息，用于调试
        console.log(`服务端返回操作信息 - 操作次数: ${操作信息.操作次数}, 最大操作次数: ${操作信息.最大操作次数}, 剩余操作次数: ${操作信息.剩余操作次数}`);
        
        storages.create("小红书操作").put("操作信息", 操作信息);
    }

    return 结果;
}

/**
 * 获取当前操作信息
 * 
 * @returns {Object} - 操作信息
 */
function 获取操作信息() {
    let 存储的信息 = storages.create("小红书操作").get("操作信息");
    if (!存储的信息) {
        return {
            操作次数: 0,
            最大操作次数: 0,
            剩余操作次数: 0
        };
    }

    // 确保剩余操作次数不是NaN
    if (isNaN(存储的信息.剩余操作次数)) {
        存储的信息.剩余操作次数 = 存储的信息.最大操作次数 - 存储的信息.操作次数;
        if (存储的信息.剩余操作次数 < 0) 存储的信息.剩余操作次数 = 0;
        storages.create("小红书操作").put("操作信息", 存储的信息);
    }

    return 存储的信息;
}

/**
 * 获取当前配置
 * 
 * @returns {Object|null} - 当前配置信息，如果没有则返回null
 */
function 获取当前配置() {
    let 当前配置 = storages.create("小红书操作").get("当前配置");
    if (!当前配置) {
        console.log("未找到当前配置");
        return null;
    }

    console.log("获取到当前配置: " + 当前配置.name);
    return 当前配置;
}

/**
 * 设置API配置
 * 
 * @param {string} 基本URL - API基本URL
 * @param {string} 用户名 - 用户名
 * @param {string} 设备令牌 - 设备令牌
 * @param {string} 设备名称 - 设备名称
 */
function 设置API配置(基本URL, 用户名, 设备令牌, 设备名称) {
    if (基本URL) API配置.基本URL = 基本URL;
    if (用户名) API配置.用户名 = 用户名;
    if (设备令牌) API配置.设备令牌 = 设备令牌;
    if (设备名称) API配置.设备名称 = 设备名称;
    console.log("API配置已更新: " + JSON.stringify(API配置));
}


/**
 * 从文本中提取数字
 * 优化版本：提高提取效率，减少日志输出
 * 
 * @param {string} text - 包含数字的文本
 * @returns {number|null} - 提取的数字，失败返回null
 */
function 提取数字(text) {
    if (!text) return null;

    try {
        // 先移除所有空格，确保能处理"点赞 1445"这种格式
        let cleanText = text.replace(/\s+/g, "");

        // 匹配数字部分（包括小数和千分位）
        let 数字匹配 = cleanText.match(/\d+(\.\d+)?/);
        if (数字匹配) {
            // 转换为数字
            let 提取结果 = parseFloat(数字匹配[0]);
            return 提取结果;
        }

        // 如果是纯数字文本，直接解析
        if (/^\d+$/.test(text)) {
            let 提取结果 = parseInt(text);
            return 提取结果;
        }

        return null;
    } catch (e) {
        return null;
    }
}

// 判断当前是否为ROOT模式
function 当前为ROOT模式() {
    return DeviceOperation.获取交互操作模式() === 3;
}
// 判断当前是否为无障碍模式
function 当前为无障碍模式() {
    return DeviceOperation.获取交互操作模式() === 1;
}

// 获取互动元素（自动分发）
function 获取互动元素() {
    try {
        if (当前为ROOT模式()) {
            console.log("ROOT模式下获取互动元素");
            return 使用ROOT获取互动元素();
        } else if (当前为无障碍模式()) {
            console.log("无障碍模式下获取互动元素");
            return 使用无障碍获取互动元素();
        } else {
            console.error("未知操作模式，无法获取互动元素");
            return null;
        }
    } catch (e) {
        console.error("获取互动元素出错: " + e.message);
        return null;
    }
}

/**
 * 使用ROOT模式获取互动元素
 * 
 * @returns {Object|null} - 互动元素信息
 */
function 使用ROOT获取互动元素() {
    try {
        // 获取当前窗口XML
        let pageXml = DeviceOperation.获取窗口XML();
        if (!pageXml) {
            console.error("获取页面XML失败");
            return null;
        }

        console.log("成功获取窗口XML，开始解析互动元素");

        // 使用DeviceOperation的解析XML互动元素方法
        let 解析结果 = DeviceOperation.解析XML互动元素(pageXml);
        if (!解析结果) {
            console.log("解析XML互动元素失败");
            return null;
        }

        console.log("解析结果:", JSON.stringify(解析结果));

        // 构建返回结果
        let 结果 = {
            点赞: 解析结果.点赞 ? 解析结果.点赞.数量 : null,
            收藏: 解析结果.收藏 ? 解析结果.收藏.数量 : null,
            评论: 解析结果.评论 ? 解析结果.评论.数量 : null,
            点赞元素: null,
            收藏元素: null,
            评论元素: null,
            是否视频: 解析结果.内容类型 === "视频"
        };

        // 创建点赞元素对象
        if (解析结果.点赞 && 解析结果.点赞.坐标) {
            结果.点赞元素 = {
                bounds: () => ({
                    centerX: () => 解析结果.点赞.坐标.x,
                    centerY: () => 解析结果.点赞.坐标.y
                }),
                desc: () => `点赞 ${解析结果.点赞.数量 || 0}`,
                点击坐标: {
                    x: 解析结果.点赞.坐标.x,
                    y: 解析结果.点赞.坐标.y
                }
            };
            console.log(`找到点赞元素: (${解析结果.点赞.坐标.x}, ${解析结果.点赞.坐标.y}), 数量: ${解析结果.点赞.数量}`);
        }

        // 创建收藏元素对象
        if (解析结果.收藏 && 解析结果.收藏.坐标) {
            结果.收藏元素 = {
                bounds: () => ({
                    centerX: () => 解析结果.收藏.坐标.x,
                    centerY: () => 解析结果.收藏.坐标.y
                }),
                desc: () => `收藏 ${解析结果.收藏.数量 || 0}`,
                点击坐标: {
                    x: 解析结果.收藏.坐标.x,
                    y: 解析结果.收藏.坐标.y
                }
            };
            console.log(`找到收藏元素: (${解析结果.收藏.坐标.x}, ${解析结果.收藏.坐标.y}), 数量: ${解析结果.收藏.数量}`);
        }

        // 创建评论元素对象
        if (解析结果.评论 && 解析结果.评论.坐标) {
            结果.评论元素 = {
                bounds: () => ({
                    centerX: () => 解析结果.评论.坐标.x,
                    centerY: () => 解析结果.评论.坐标.y
                }),
                desc: () => `评论 ${解析结果.评论.数量 || 0}`,
                点击坐标: {
                    x: 解析结果.评论.坐标.x,
                    y: 解析结果.评论.坐标.y
                }
            };
            console.log(`找到评论元素: (${解析结果.评论.坐标.x}, ${解析结果.评论.坐标.y}), 数量: ${解析结果.评论.数量}`);
        }

        console.log(`内容类型: ${解析结果.内容类型}, 是否视频: ${结果.是否视频}`);

        if (结果.点赞元素 || 结果.收藏元素 || 结果.评论元素) {
            return 结果;
        }

        console.log("未找到任何互动元素");
        return null;
    } catch (e) {
        console.error("使用ROOT获取互动元素出错: " + e.message);
        return null;
    }
}

/**
 * 使用无障碍服务获取互动元素
 * 
 * @returns {Object|null} - 互动元素信息
 */
function 使用无障碍获取互动元素() {
    try {
        // 初始化返回结果
        let 结果 = {
            点赞: null,
            收藏: null,
            评论: null,
            点赞元素: null,
            收藏元素: null,
            评论元素: null,
            是否视频: false
        };

        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;

        // 定义视频互动区域（通常在屏幕右侧）
        let 视频互动区域 = {
            left: Math.floor(屏幕宽度 * 0.7),  // 屏幕右侧30%区域
            top: Math.floor(屏幕高度 * 0.3),   // 从屏幕30%位置开始
            right: 屏幕宽度,
            bottom: 屏幕高度 * 0.8             // 到屏幕80%位置结束
        };

        // 定义普通互动区域（通常在屏幕下半部分）
        let 普通互动区域 = {
            left: 0,
            top: Math.floor(屏幕高度 * 0.5),  // 从屏幕中间开始
            right: 屏幕宽度,
            bottom: 屏幕高度
        };

        // 首先检查是否为视频页面 - 使用更高效的方法
        let 视频区域按钮 = className("android.widget.Button")
            .boundsInside(视频互动区域.left, 视频互动区域.top, 视频互动区域.right, 视频互动区域.bottom)
            .find();

        let 是否视频页面 = 视频区域按钮.length > 0;
        结果.是否视频 = 是否视频页面;
        页面信息.内容类型 = 是否视频页面 ? "视频" : "图文";

        // 根据页面类型选择互动区域
        let 互动区域 = 是否视频页面 ? 视频互动区域 : 普通互动区域;

        // 1. 获取用户名
        let 用户名元素 = textMatches(".*").findOne(1000);
        if (用户名元素) {
            let 文本内容 = 用户名元素.text();
            if (文本内容 && 文本内容.length > 0 && 文本内容.length < 20) {
                页面信息.用户名 = 文本内容;
                console.log(`找到用户名: ${页面信息.用户名}`);
            }
        }

        // 2. 获取标题和内容
        let 所有文本元素 = textMatches(".*").find();
        for (let i = 0; i < 所有文本元素.length; i++) {
            let 文本内容 = 所有文本元素[i].text();
            if (!文本内容 || 文本内容.length < 5) continue;

            // 跳过包含特殊字符的文本
            if (文本内容.includes("#") || 文本内容.includes("@") || 文本内容.includes("http")) {
                continue;
            }

            // 如果还没有标题，第一个较长的文本作为标题
            if (!页面信息.标题 && 文本内容.length > 5 && 文本内容.length < 100) {
                页面信息.标题 = 文本内容;
                console.log(`找到标题: ${页面信息.标题}`);
            }
            // 如果已有标题，较长的文本作为内容
            else if (页面信息.标题 && 文本内容.length > 20 && !页面信息.内容) {
                页面信息.内容 = 文本内容;
                console.log(`找到内容: ${页面信息.内容.substring(0, 50)}...`);
                break;
            }
        }

        // 3. 获取互动数据
        // 查找点赞元素
        let 点赞元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*点赞.*")
            .findOne(500);

        if (点赞元素) {
            let desc = 点赞元素.desc();
            页面信息.已点赞 = desc.includes("已点赞");
            // 如果desc只包含"点赞"而没有数字，说明是0
            // 但如果显示"已点赞"，说明用户已经点赞，数量至少为1
            if (desc === "点赞") {
                页面信息.点赞数 = 0;
            } else if (desc === "已点赞") {
                页面信息.点赞数 = 1; // 已点赞时数量至少为1
            } else {
                页面信息.点赞数 = 提取数字(desc);
            }
            console.log(`找到点赞信息: ${页面信息.已点赞 ? "已点赞" : "未点赞"} ${页面信息.点赞数}`);
        }

        // 查找收藏元素
        let 收藏元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*收藏.*")
            .findOne(500);

        if (收藏元素) {
            let desc = 收藏元素.desc();
            页面信息.已收藏 = desc.includes("已收藏");
            // 如果desc只包含"收藏"而没有数字，说明是0
            // 但如果显示"已收藏"，说明用户已经收藏，数量至少为1
            if (desc === "收藏") {
                页面信息.收藏数 = 0;
            } else if (desc === "已收藏") {
                页面信息.收藏数 = 1; // 已收藏时数量至少为1
            } else {
                页面信息.收藏数 = 提取数字(desc);
            }
            console.log(`找到收藏信息: ${页面信息.已收藏 ? "已收藏" : "未收藏"} ${页面信息.收藏数}`);
        }

        // 查找评论元素
        let 评论元素 = className("android.widget.Button")
            .boundsInside(互动区域.left, 互动区域.top, 互动区域.right, 互动区域.bottom)
            .descMatches(".*评论.*")
            .findOne(500);

        if (评论元素) {
            let desc = 评论元素.desc();
            // 如果desc只包含"评论"而没有数字，说明是0
            if (desc === "评论") {
                页面信息.评论数 = 0;
            } else {
                页面信息.评论数 = 提取数字(desc);
            }
            console.log(`找到评论数: ${页面信息.评论数}`);
        }

        // 如果在指定区域没有找到互动元素，尝试扩大搜索范围
        if (页面信息.点赞数 === null && 页面信息.收藏数 === null) {
            点赞元素 = className("android.widget.Button").descMatches(".*点赞.*").findOne(500);
            收藏元素 = className("android.widget.Button").descMatches(".*收藏.*").findOne(500);
            评论元素 = className("android.widget.Button").descMatches(".*评论.*").findOne(500);

            if (点赞元素) {
                let desc = 点赞元素.desc();
                页面信息.已点赞 = desc.includes("已点赞");
                // 如果desc只包含"点赞"而没有数字，说明是0
                // 但如果显示"已点赞"，说明用户已经点赞，数量至少为1
                if (desc === "点赞") {
                    页面信息.点赞数 = 0;
                } else if (desc === "已点赞") {
                    页面信息.点赞数 = 1; // 已点赞时数量至少为1
                } else {
                    页面信息.点赞数 = 提取数字(desc);
                }
                console.log(`从全屏找到点赞信息: ${页面信息.已点赞 ? "已点赞" : "未点赞"} ${页面信息.点赞数}`);
            }

            if (收藏元素) {
                let desc = 收藏元素.desc();
                页面信息.已收藏 = desc.includes("已收藏");
                // 如果desc只包含"收藏"而没有数字，说明是0
                // 但如果显示"已收藏"，说明用户已经收藏，数量至少为1
                if (desc === "收藏") {
                    页面信息.收藏数 = 0;
                } else if (desc === "已收藏") {
                    页面信息.收藏数 = 1; // 已收藏时数量至少为1
                } else {
                    页面信息.收藏数 = 提取数字(desc);
                }
                console.log(`从全屏找到收藏信息: ${页面信息.已收藏 ? "已收藏" : "未收藏"} ${页面信息.收藏数}`);
            }

            if (评论元素) {
                let desc = 评论元素.desc();
                // 如果desc只包含"评论"而没有数字，说明是0
                if (desc === "评论") {
                    页面信息.评论数 = 0;
                } else {
                    页面信息.评论数 = 提取数字(desc);
                }
                console.log(`从全屏找到评论数: ${页面信息.评论数}`);
            }
        }

        // 检查是否获取到有效信息
        if (页面信息.标题 || 页面信息.用户名 || 页面信息.点赞数 !== null || 页面信息.收藏数 !== null) {
            console.log("页面信息获取完成:", JSON.stringify(页面信息));
            return 页面信息;
        }

        return null;
    } catch (e) {
        console.error("使用无障碍获取页面信息出错: " + e.message);
        return null;
    }
}

/**
 * 比较两个页面信息，判断是否为同一篇文章
 * 优化版本：提取最长的3段文本进行比较，不区分标题和内容
 * 
 * @param {Object} 信息1 - 第一个页面信息
 * @param {Object} 信息2 - 第二个页面信息
 * @returns {boolean} - 是否为同一篇文章
 */
function 比较页面信息(信息1, 信息2) {
    // 如果任一信息为空，返回false
    if (!信息1 || !信息2) {
        console.log("[比对] 有页面信息为空，直接判定不一致");
        return false;
    }

    // 确保最长文本数组存在
    信息1.最长文本列表 = 信息1.最长文本列表 || [];
    信息2.最长文本列表 = 信息2.最长文本列表 || [];

    // 输出详细调试信息
    console.log("[比对] 用户名1:", 信息1.用户名 || "null", "用户名2:", 信息2.用户名 || "null");

    // 输出最长文本列表
    console.log("━━━━━━━━━━ 文章1最长文本 ━━━━━━━━━━");
    for (let i = 0; i < 信息1.最长文本列表.length; i++) {
        let 文本 = 信息1.最长文本列表[i] || "";
        let 显示文本 = 文本.length > 100 ? 文本.substring(0, 100) + "..." : 文本;
        console.log(`[长文本1-${i + 1}] (${文本.length}字): ${显示文本}`);
    }

    console.log("━━━━━━━━━━ 文章2最长文本 ━━━━━━━━━━");
    for (let i = 0; i < 信息2.最长文本列表.length; i++) {
        let 文本 = 信息2.最长文本列表[i] || "";
        let 显示文本 = 文本.length > 100 ? 文本.substring(0, 100) + "..." : 文本;
        console.log(`[长文本2-${i + 1}] (${文本.length}字): ${显示文本}`);
    }

    // 1. 检查作者是否相同
    let 作者相同 = 信息1.用户名 && 信息2.用户名 && 信息1.用户名 === 信息2.用户名;
    if (!作者相同) {
        console.log("[比对] 用户名不一致");
    }

    // 2. 检查最长文本是否有相似项
    let 有相似文本 = false;
    let 相似文本数量 = 0;
    let 相似文本列表 = [];

    // 如果两篇文章都有最长文本列表，检查它们是否有相同/相似项
    if (信息1.最长文本列表.length > 0 && 信息2.最长文本列表.length > 0) {
        // 遍历第一篇文章的最长文本
        for (let i = 0; i < 信息1.最长文本列表.length; i++) {
            let 文本1 = 信息1.最长文本列表[i];
            if (!文本1 || 文本1.length < 5) continue;

            // 针对每个文本1，检查是否与文本2中的任何一项相似
            for (let j = 0; j < 信息2.最长文本列表.length; j++) {
                let 文本2 = 信息2.最长文本列表[j];
                if (!文本2 || 文本2.length < 5) continue;

                // 尝试多种比较方法
                // 1. 直接比较前30个字符
                const 比较长度1 = Math.min(30, 文本1.length, 文本2.length);
                const 文本1前缀1 = 文本1.substring(0, 比较长度1);
                const 文本2前缀1 = 文本2.substring(0, 比较长度1);

                // 2. 比较中间部分（避免开头不同但内容相同的情况）
                const 比较长度2 = Math.min(30, 文本1.length - 30, 文本2.length - 30);
                const 文本1中间 = 比较长度2 > 0 ? 文本1.substring(30, 60) : "";
                const 文本2中间 = 比较长度2 > 0 ? 文本2.substring(30, 60) : "";

                // 3. 比较结尾部分
                const 比较长度3 = Math.min(30, 文本1.length, 文本2.length);
                const 文本1结尾 = 文本1.length > 比较长度3 ? 文本1.substring(文本1.length - 比较长度3) : 文本1;
                const 文本2结尾 = 文本2.length > 比较长度3 ? 文本2.substring(文本2.length - 比较长度3) : 文本2;

                // 任意一种方式匹配即算相似
                if (文本1前缀1 === 文本2前缀1 ||
                    (文本1中间.length > 0 && 文本1中间 === 文本2中间) ||
                    文本1结尾 === 文本2结尾) {

                    相似文本数量++;
                    相似文本列表.push({
                        匹配方式: 文本1前缀1 === 文本2前缀1 ? "前缀匹配" :
                            (文本1中间 === 文本2中间 ? "中间部分匹配" : "结尾匹配"),
                        前缀1: 文本1前缀1,
                        前缀2: 文本2前缀1,
                        文本1: 文本1.substring(0, Math.min(200, 文本1.length)),
                        文本2: 文本2.substring(0, Math.min(200, 文本2.length))
                    });
                    console.log(`[比对] 找到相似文本 #${相似文本数量}: 匹配方式="${相似文本列表[相似文本数量 - 1].匹配方式}"`);
                    break; // 找到一个相似项就继续检查下一个文本1
                }
            }
        }

        // 如果找到至少一个相似文本，认为有相似性
        有相似文本 = 相似文本数量 > 0;
    }

    if (!有相似文本) {
        console.log("[比对] 没有找到相似的文本内容");
    } else {
        console.log(`[比对] 共找到 ${相似文本数量} 个相似文本`);
        for (let i = 0; i < 相似文本列表.length; i++) {
            console.log(`  - 相似文本${i + 1} (${相似文本列表[i].匹配方式}):`);
            console.log(`    文本1: ${相似文本列表[i].文本1}`);
            console.log(`    文本2: ${相似文本列表[i].文本2}`);
        }
    }

    // 3. 综合判断是否是同一篇文章
    // 如果用户名相同且有相似文本，或者相似文本数量>=2，认为是同一篇文章
    let 最终结果 = (作者相同 && 有相似文本) || 相似文本数量 >= 1;
    console.log(`[比对] 最终判定: ${最终结果 ? "一致" : "不一致"}`);
    return 最终结果;
}

/**
 * 显示性能优化总结
 * 在脚本启动时显示性能优化信息
 */
function 显示性能优化总结() {
    console.log("========== 小红书自动点赞脚本性能优化总结 ==========");
    console.log("1. 元素查找优化");
    console.log("   - 使用更精确的选择器，减少查找次数");
    console.log("   - 限制元素查找范围，只在可能区域内查找");
    console.log("   - 使用findOne替代find，提高查询效率");
    console.log("   - 优先使用descMatches而非遍历所有元素");

    console.log("2. 页面信息获取优化");
    console.log("   - 一次性获取所有文本元素，避免多次查询");
    console.log("   - 使用缓存避免重复获取相同信息");
    console.log("   - 优化文本过滤算法，提高筛选效率");
    console.log("   - 区分视频页面和图文页面的处理逻辑");

    console.log("3. 日志输出优化");
    console.log("   - 减少不必要的日志输出，降低I/O开销");
    console.log("   - 仅保留关键信息的输出，提高执行效率");

    console.log("4. 异常处理优化");
    console.log("   - 简化try-catch结构，减少嵌套层级");
    console.log("   - 统一错误处理逻辑，提高代码稳定性");

    console.log("5. 页面比较优化");
    console.log("   - 使用更高效的页面信息比较算法");
    console.log("   - 优先比较关键特征，快速判断页面相似性");

    console.log("=================================================");
}

// 在模块加载时显示优化信息
// 显示性能优化总结();

/**
 * 执行点赞操作
 * 
 * @returns {Object} - 点赞结果
 */
function 执行点赞() {
    console.log("执行点赞操作");

    try {
        // 只检查是否已点赞，不尝试坐标点击
/*         let 互动元素 = 获取互动元素();
        if (互动元素 && 互动元素.点赞元素) {
            let desc = 互动元素.点赞元素.desc();
            if (desc && desc.includes("已点赞")) {
                console.log("已经点过赞了");
                return { 成功: true, 信息: "已经点过赞了" };
            }
        } */

        // 无论是否找到互动元素，都执行随机点击策略
        console.log("使用随机点击策略执行点赞");

        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;

        // 屏幕中间位置
        let 中心X = 屏幕宽度 / 2;
        let 中心Y = 屏幕高度 / 2;

        // 生成随机点击次数（3-6次）
        let 点击次数 = Math.floor(Math.random() * 4) + 1;
        console.log(`将在屏幕中间区域随机点击 ${点击次数} 次`);

        // 第一次点击位置 - 在中心点周围随机100像素范围内生成坐标
        let 当前X = 中心X + (Math.random() * 200 - 100);
        let 当前Y = 中心Y + (Math.random() * 200 - 100);

        // 确保坐标在屏幕内
        当前X = Math.max(10, Math.min(屏幕宽度 - 10, 当前X));
        当前Y = Math.max(10, Math.min(屏幕高度 - 10, 当前Y));

        // 执行随机点击
        for (let i = 0; i < 点击次数; i++) {
            // 后续点击在前一次位置基础上偏移10像素内
            if (i > 0) {
                当前X += (Math.random() * 20 - 10); // ±10像素偏移
                当前Y += (Math.random() * 20 - 10); // ±10像素偏移

                // 确保坐标在屏幕内
                当前X = Math.max(10, Math.min(屏幕宽度 - 10, 当前X));
                当前Y = Math.max(10, Math.min(屏幕高度 - 10, 当前Y));
            }

            //console.log(`随机点击 #${i + 1}: (${Math.floor(当前X)}, ${Math.floor(当前Y)})`);

            // 直接使用DeviceOperation.点击，不需要再判断模式
            DeviceOperation.点击(当前X, 当前Y);

            // 随机等待20-50毫秒
            let min = 8;
            let max = 16;
            let 随机等待时间 = Math.floor(Math.random() * (max - min + 1)) + min;
            sleep(随机等待时间);
        }

        // 最后等待500毫秒
        //等待(500);

        return { 成功: true, 信息: `随机点击策略完成，点击了 ${点击次数} 次` };
    } catch (e) {
        console.error("执行点赞出错: " + e.message);
        return { 成功: false, 信息: "执行点赞出错: " + e.message };
    }
}

/**
 * 执行收藏操作
 * 
 * @returns {Object} - 收藏结果
 */
function 执行收藏() {
    console.log("执行收藏操作");

    try {
        // 只检查是否已收藏，不尝试坐标点击
/*         let 互动元素 = 获取互动元素();
        if (互动元素 && 互动元素.收藏元素) {
            let desc = 互动元素.收藏元素.desc();
            if (desc && desc.includes("已收藏")) {
                console.log("已经收藏过了");
                return { 成功: true, 信息: "已经收藏过了" };
            }
        } */

        // 直接使用随机点击策略
        console.log("使用随机点击策略执行收藏");

        // 获取屏幕尺寸
        let 屏幕宽度 = device.width;
        let 屏幕高度 = device.height;

        // 屏幕中间位置，稍微偏右下一点（收藏按钮通常在点赞按钮右侧）
        let 中心X = (屏幕宽度 / 2) + 150;  // 更往右偏移，确保点到收藏按钮
        let 中心Y = (屏幕高度 / 2);

        // 生成随机点击次数（3-5次）
        let 点击次数 = Math.floor(Math.random() * 3) + 3;
        console.log(`将在屏幕中间偏右区域随机点击 ${点击次数} 次`);

        // 第一次点击位置 - 在中心点周围随机100像素范围内生成坐标
        let 当前X = 中心X + (Math.random() * 200 - 100);
        let 当前Y = 中心Y + (Math.random() * 200 - 100);

        // 确保坐标在屏幕内
        当前X = Math.max(10, Math.min(屏幕宽度 - 10, 当前X));
        当前Y = Math.max(10, Math.min(屏幕高度 - 10, 当前Y));

        // 执行随机点击
        for (let i = 0; i < 点击次数; i++) {
            // 后续点击在前一次位置基础上偏移10像素内
            if (i > 0) {
                当前X += (Math.random() * 20 - 10); // ±10像素偏移
                当前Y += (Math.random() * 20 - 10); // ±10像素偏移

                // 确保坐标在屏幕内
                当前X = Math.max(10, Math.min(屏幕宽度 - 10, 当前X));
                当前Y = Math.max(10, Math.min(屏幕高度 - 10, 当前Y));
            }

            console.log(`随机点击 #${i + 1}: (${Math.floor(当前X)}, ${Math.floor(当前Y)})`);

            // 直接使用DeviceOperation.点击，不需要再判断模式
            DeviceOperation.点击(当前X, 当前Y);

            // 随机等待20-50毫秒
            let 随机等待时间 = Math.floor(Math.random() * 31) + 20;
            sleep(随机等待时间);
        }

        // 最后等待500毫秒
        等待(500);

        return { 成功: true, 信息: `随机点击策略完成，点击了 ${点击次数} 次` };
    } catch (e) {
        console.error("执行收藏出错: " + e.message);
        return { 成功: false, 信息: "执行收藏出错: " + e.message };
    }
}

/**
 * 开始小红书点赞操作
 * 主要流程：获取链接 -> 打开小红书 -> 执行点赞和收藏操作 -> 更新状态
 * 
 * @returns {Object} - 操作结果
 */
function 开始小红书点赞操作() {
    console.log("开始循环操作");

    // 清除存储的设备令牌，强制重新生成
    清除设备令牌();

    // 确保首次运行标志为true，生成新的设备令牌
    是否首次运行 = true;

    // 注册设备
    console.log("注册设备");
    let 注册结果 = 设备注册();
    if (!注册结果.success) {
        console.error("设备注册失败: " + 注册结果.message);
        toast("设备注册失败: " + 注册结果.message);
        // 如果是达到上限，直接return
        if (注册结果.limitReached) {
            return { success: false, message: "今日操作次数已达上限" };
        }
        return { success: false, message: "设备注册失败: " + 注册结果.message };
    }

    // 注意：不在这里检查剩余操作次数，因为需要先从服务端获取最新数据
    // 剩余操作次数的检查将在获取链接后进行

    // 获取"操作几次恢复出厂设置"配置
    let 操作几次恢复出厂设置 = 获取配置项("操作几次恢复出厂设置", 0);
    console.log(`配置项: 操作几次恢复出厂设置 = ${操作几次恢复出厂设置}`);

    // 打开小红书应用
    if (!打开小红书()) {
        toast("打开小红书应用失败");
        return { success: false, message: "打开小红书应用失败" };
    }

    // 检查是否出现账号异常提示
    if (检查账号异常提示()) {
        console.log("检测到账号异常提示，需要更换账号，结束当前操作");
        toast("检测到账号异常，需要更换账号");
        return { success: false, message: "账号异常，需要更换账号", needChangeAccount: true };
    }

    // 获取操作信息
    let 操作信息 = 获取操作信息();
    console.log("当前操作信息: " + JSON.stringify(操作信息));

    // 初始化计数器
    let 已完成操作次数 = 0;
    let 最大循环次数 = 操作信息.最大操作次数 || 6; // 从服务端获取最大操作次数，默认6次

    console.log(`开始循环操作，从服务端获取最大操作次数: ${最大循环次数} 次`);
    toast(`开始循环操作，将执行 ${最大循环次数} 次操作`);

    // 主循环 - 按固定次数循环
    while (已完成操作次数 < 最大循环次数) {
        // 检查是否需要恢复出厂设置
        // if (操作几次恢复出厂设置 > 0 && 已完成操作次数 > 0 && 已完成操作次数 % 操作几次恢复出厂设置 === 0) {
        //     console.log(`已完成 ${已完成操作次数} 次操作，达到恢复出厂设置条件（每 ${操作几次恢复出厂设置} 次）`);
        //     toast(`已完成 ${已完成操作次数} 次操作，达到恢复出厂设置条件（每 ${操作几次恢复出厂设置} 次）`);
        //     return { success: true, message: "已达到恢复出厂设置条件，即将退出本次循环" };
        // }

        // 获取链接
        console.log(`[${已完成操作次数 + 1}/${最大循环次数}] 获取链接中...`);
        let 获取链接结果 = 获取下一个链接();

        // 如果获取链接失败或没有链接可用，等待一段时间后重试
        if (!获取链接结果.success || 获取链接结果.status !== "success") {
            console.log("获取链接失败或暂无链接，等待30秒后重试");
            toast("暂无链接，等待30秒后重试，等待中...");
            // 等待30秒
            for (let i = 30; i > 0; i--) {
                if (i % 5 === 0) { // 每5秒输出一次日志和提示
                    console.log(`暂无链接，等待中...(${i}秒)`);
                    toast(`暂无链接，等待中...(${i}秒)`);
                }
                sleep(1000);
            }
            continue; // 继续下一次循环，不增加计数
        }

        // 获取链接信息
        let 链接 = 获取链接结果.data.url;
        let 链接ID = 获取链接结果.data.link_id;

        console.log(`获取到链接: ${链接}`);
        console.log(`链接ID: ${链接ID}`);

        // 保存链接ID
        storages.create("小红书操作").put("当前链接ID", 链接ID);

        // 打开链接
        console.log("打开链接...");
        if (!打开链接(链接)) {
            console.error("打开链接失败");
            toast("打开链接失败");
            更新操作状态(链接ID, "failed", "both", 0, 0, "打开链接失败");
            continue;
        }

        // 获取目标页面信息
        console.log("获取目标页面信息...");
        let 目标内容 = 获取页面信息();
        if (!目标内容) {
            console.log("未获取到目标页面信息，可能小红书未打开或未进入正确页面");
            更新操作状态(链接ID, "failed", "both", 0, 0, "未获取到目标页面信息");
            continue;
        }

        console.log("成功获取目标页面信息：", 目标内容.标题 || 目标内容.内容);

        // 从目标内容中获取点赞和收藏信息
        let 操作前点赞数 = 目标内容.点赞数 || 0;
        let 操作前收藏数 = 目标内容.收藏数 || 0;
        let 已点赞 = 目标内容.已点赞 || false;
        let 已收藏 = 目标内容.已收藏 || false;
        
        console.log(`目标页面数据 - 点赞数: ${操作前点赞数}, 收藏数: ${操作前收藏数}`);
        console.log(`目标页面状态 - 已点赞: ${已点赞 ? "是" : "否"}, 已收藏: ${已收藏 ? "是" : "否"}`);
        
        // 获取任务类型（从服务端返回的数据中获取，默认为both）
        let 任务类型 = 获取链接结果.data.operation_type || "both";
        let 需要点赞 = 任务类型 === "like" || 任务类型 === "both";
        let 需要收藏 = 任务类型 === "collect" || 任务类型 === "both";
        
        // 调试：显示服务端返回的完整数据
        console.log("服务端返回的完整数据: " + JSON.stringify(获取链接结果.data));

        // 获取目标点赞数和收藏数（从服务端返回的数据中获取）
        let 目标点赞数 = 获取链接结果.data.target_likes;
        let 目标收藏数 = 获取链接结果.data.target_collects;

        console.log(`任务类型: ${任务类型}, 需要点赞: ${需要点赞}, 需要收藏: ${需要收藏}`);
        console.log(`目标点赞数: ${目标点赞数}, 目标收藏数: ${目标收藏数}`);
        console.log(`当前状态: 已点赞=${已点赞}, 已收藏=${已收藏}, 当前点赞数=${操作前点赞数}, 当前收藏数=${操作前收藏数}`);
        
        // 判断是否需要继续操作
        let 需要操作 = false;
        let 点赞任务完成 = false;
        let 收藏任务完成 = false;

        // 详细的点赞任务判断
        if (需要点赞) {
            if (已点赞) {
                console.log("✅ 点赞任务已完成 - 已经点过赞了");
                点赞任务完成 = true;
            } else if (目标点赞数 !== undefined && 目标点赞数 > 0 && 操作前点赞数 >= 目标点赞数) {
                console.log(`✅ 点赞任务已完成 - 当前点赞数(${操作前点赞数})已达到目标(${目标点赞数})`);
                点赞任务完成 = true;
            } else {
                console.log(`❌ 需要执行点赞操作 - 当前状态: 已点赞=${已点赞}, 当前点赞数=${操作前点赞数}, 目标点赞数=${目标点赞数}`);
                需要操作 = true;
            }
        } else {
            console.log("⏭️ 任务不需要点赞");
            点赞任务完成 = true; // 不需要点赞就算完成
        }

        // 详细的收藏任务判断
        if (需要收藏) {
            if (已收藏) {
                console.log("✅ 收藏任务已完成 - 已经收藏过了");
                收藏任务完成 = true;
            } else if (目标收藏数 !== undefined && 目标收藏数 > 0 && 操作前收藏数 >= 目标收藏数) {
                console.log(`✅ 收藏任务已完成 - 当前收藏数(${操作前收藏数})已达到目标(${目标收藏数})`);
                收藏任务完成 = true;
            } else {
                console.log(`❌ 需要执行收藏操作 - 当前状态: 已收藏=${已收藏}, 当前收藏数=${操作前收藏数}, 目标收藏数=${目标收藏数}`);
                需要操作 = true;
            }
        } else {
            console.log("⏭️ 任务不需要收藏");
            收藏任务完成 = true; // 不需要收藏就算完成
        }

        // 如果所有任务都完成了，就不需要操作
        if (点赞任务完成 && 收藏任务完成 && !需要操作) {
            console.log("🎉 所有任务都已完成，无需操作");
        }
        
        // 如果不需要操作，直接更新状态并继续下一个任务
        if (!需要操作) {
            //console.log("当前任务已完成，无需操作");
            更新操作状态(链接ID, "success", 任务类型, 操作前点赞数, 操作前收藏数);
            // 无论是否需要操作，都计数为已完成一次操作（因为获取了链接）
            已完成操作次数++;
            console.log(`[主循环] 已完成操作次数: ${已完成操作次数}/${最大循环次数}`);
            continue;
        }

        // 返回首页
        console.log("返回首页...");
        if (!返回主界面()) {
            console.log("返回首页失败");
            更新操作状态(链接ID, "failed", 任务类型, 操作前点赞数, 操作前收藏数, "返回首页失败");
            // 无论成功失败，都计数为已完成一次操作
            已完成操作次数++;
            console.log(`[主循环] 已完成操作次数: ${已完成操作次数}/${最大循环次数}`);
            continue;
        }

        // 等待首页加载完成
        //sleep(2000);

        // 使用新方法获取第一个点赞位置并直接点赞
        console.log("获取第一个点赞位置坐标...");
        let 点赞位置 = 获取第一个点赞位置();

        if (!点赞位置) {
            console.log("获取第一个点赞位置失败");
            更新操作状态(链接ID, "failed", 任务类型, 操作前点赞数, 操作前收藏数, "获取第一个点赞位置失败");
            // 无论成功失败，都计数为已完成一次操作
            已完成操作次数++;
            console.log(`[主循环] 已完成操作次数: ${已完成操作次数}/${最大循环次数}`);
            continue;
        }

        console.log(`成功获取点赞位置: (${点赞位置.x}, ${点赞位置.y})`);

        // 实际执行的操作类型，只执行点赞操作
        let 实际操作类型 = "like";
        let 点赞结果 = { 成功: false, 信息: "未执行" };

        // 检查是否需要执行点赞操作
        if (需要点赞) {
            console.log("开始执行点赞操作...");
            console.log(`点击坐标: (${点赞位置.x}, ${点赞位置.y})`);

            // 直接点击点赞位置
            DeviceOperation.点击(点赞位置.x, 点赞位置.y);
            sleep(1000); // 等待点击响应

            // 简单判断点赞是否成功（这里可以根据需要添加更复杂的验证逻辑）
            点赞结果 = { 成功: true, 信息: "点赞操作已执行" };
            console.log("点赞操作已执行");
        } else {
            console.log("任务不需要点赞，跳过点赞操作");
            点赞结果 = { 成功: true, 信息: "无需点赞" };
        }

        // 收藏操作暂时跳过（按需求不执行收藏）
        console.log("收藏操作暂时跳过（按需求不执行收藏）");

        // 更新操作状态
        let 操作状态 = 点赞结果.成功 ? "success" : "failed";
        let 错误信息 = null;
        if (!点赞结果.成功) {
            错误信息 = "点赞操作失败";
        }

        let 更新结果 = 更新操作状态(链接ID, 操作状态, 实际操作类型, 操作前点赞数, 操作前收藏数, 错误信息);
        console.log(`操作结果 - 点赞: ${点赞结果.成功 ? "成功" : "失败"}, 收藏: 已跳过`);

        // 无论成功失败，都计数为已完成一次操作
        已完成操作次数++;
        console.log(`[主循环] 已完成操作次数: ${已完成操作次数}/${最大循环次数}`);

        // 返回主界面
        返回主界面();

        /* ========== 以下代码已注释掉：原来的进入文章页面逻辑 ========== */
        /*
        // 点击首页第一篇文章
        console.log("点击首页第一篇文章...");
        if (!点击首篇文章()) {
            console.log("点击首页第一篇文章失败");
            更新操作状态(链接ID, "failed", 任务类型, 操作前点赞数, 操作前收藏数, "点击首页第一篇文章失败");
            continue;
        }

        // 获取首页文章信息
        let 首页内容 = null;
        // 等待文章页面加载并获取信息，最多尝试10次
        for (let i = 0; i < 10; i++) {
            console.log(`尝试获取首页文章信息...(${i+1}/10)`);
            首页内容 = 获取页面信息();
            if (首页内容) {
                console.log("成功获取首页文章信息");
                break;
            } else {
                sleep(1000);
            }
        }

        if (!首页内容) {
            console.log("未获取到首页文章信息");
            更新操作状态(链接ID, "failed", 任务类型, 操作前点赞数, 操作前收藏数, "未获取到首页文章信息");
            continue;
        }

        console.log("成功获取首页文章信息：", 首页内容.标题 || 首页内容.内容);

        // 比对两次内容
        console.log("比对目标内容和首页内容...");
        if (!比较页面信息(目标内容, 首页内容)) {
            console.log("内容不匹配，不是同一篇文章");
            更新操作状态(链接ID, "failed", 任务类型, 操作前点赞数, 操作前收藏数, "内容不匹配，不是同一篇文章");
            continue;
        }

        console.log("内容匹配，确认是同一篇文章，直接执行点赞操作");

        // 实际执行的操作类型，默认与任务类型相同
        let 实际操作类型 = 任务类型;
        let 执行了点赞 = false;
        let 执行了收藏 = false;

        // 检查是否需要执行点赞操作
        let 点赞结果 = { 成功: true, 信息: "无需点赞" };
        if (需要点赞 && (!首页内容.已点赞 || (目标点赞数 !== undefined && 首页内容.点赞数 < 目标点赞数))) {
            点赞结果 = 执行点赞();
            执行了点赞 = true;
            sleep(500);
        } else if (需要点赞) {
            console.log("文章已被点赞或点赞数已达标，跳过点赞操作");
        }

        // 检查是否需要执行收藏操作
        let 收藏结果 = { 成功: true, 信息: "无需收藏" };
        if (需要收藏 && (!首页内容.已收藏 || (目标收藏数 !== undefined && 首页内容.收藏数 < 目标收藏数))) {
            收藏结果 = 执行收藏();
            执行了收藏 = true;
        } else if (需要收藏) {
            console.log("文章已被收藏或收藏数已达标，跳过收藏操作");
        }

        // 根据实际执行的操作调整操作类型
        if (执行了点赞 && 执行了收藏) {
            实际操作类型 = "both";
        } else if (执行了点赞) {
            实际操作类型 = "like";
        } else if (执行了收藏) {
            实际操作类型 = "collect";
        }

        // 更新操作状态
        let 操作状态 = 点赞结果.成功 && 收藏结果.成功 ? "success" : "failed";
        let 错误信息 = null;
        if (!点赞结果.成功 && 执行了点赞) {
            错误信息 = "点赞操作失败";
        }
        if (!收藏结果.成功 && 执行了收藏) {
            错误信息 = 错误信息 ? 错误信息 + ", 收藏操作失败" : "收藏操作失败";
        }

        let 更新结果 = 更新操作状态(链接ID, 操作状态, 实际操作类型, 操作前点赞数, 操作前收藏数, 错误信息);
        console.log(`操作结果 - 点赞: ${点赞结果.成功 ? "成功" : "失败"}, 收藏: ${收藏结果.成功 ? "成功" : "失败"}`);

        // 返回主界面
        返回主界面();
        */
        /* ========== 注释结束 ========== */

        // 注意：计数已在上面的操作完成后进行，这里不需要重复计数

        // 更新操作信息
        更新操作信息(更新结果);
        
        // 检查是否需要退出循环
        // if (操作信息.剩余操作次数 <= 0) {
        //     console.log("剩余操作次数为0，退出循环");
        //     break;
        // }
    }

    // 循环结束，清理注册信息，确保下次重新注册
    console.log("循环操作完成，清理注册信息");
    清除设备令牌();
    storages.create("小红书操作").remove("设备ID");
    storages.create("小红书操作").remove("操作信息");
    是否首次运行 = true;

    console.log(`循环操作完成，共完成 ${已完成操作次数}/${最大循环次数} 次操作`);
    toast(`循环操作完成，共完成 ${已完成操作次数}/${最大循环次数} 次操作`);
    return { success: true, message: `循环操作完成，共完成 ${已完成操作次数}/${最大循环次数} 次操作` };
}

/**
 * 更新本地操作信息
 * 
 * @param {Object} 更新结果 - 更新操作状态API的返回结果
 */
function 更新操作信息(更新结果) {
    // 获取当前操作信息
    操作信息 = 获取操作信息();
    
    // 使用服务端返回的剩余操作次数更新
    if (更新结果 && 更新结果.success && 更新结果.data && 更新结果.data.remaining_operations !== undefined) {
        操作信息.剩余操作次数 = 更新结果.data.remaining_operations;
        console.log(`从服务端获取剩余操作次数: ${操作信息.剩余操作次数}`);
    }
    
    console.log(`当前操作信息 - 剩余: ${操作信息.剩余操作次数}`);
    
    // 检查是否已达到上限
    // if (操作信息.剩余操作次数 <= 0) {
    //     console.log("已达到每日操作上限");
    //     toast("今日操作次数已达上限");
    // }
}

/**
 * 等待指定时间
 * 
 * @param {number} 毫秒 - 等待的毫秒数
 */
function 等待(毫秒) {
    sleep(毫秒);
}

/**
 * 随机等待一段时间
 * 
 * @param {number} 最小毫秒 - 最小等待时间
 * @param {number} 最大毫秒 - 最大等待时间
 */
function 随机等待(最小毫秒, 最大毫秒) {
    let 等待时间 = 最小毫秒 + Math.floor(Math.random() * (最大毫秒 - 最小毫秒));
    console.log("随机等待 " + 等待时间 + " 毫秒");
    等待(等待时间);
}

// 在等待函数后添加以下函数

/**
 * 清除存储的设备令牌
 * 强制重新生成设备令牌
 */
function 清除设备令牌() {
    console.log("清除存储的设备令牌");
    storages.create("小红书操作").remove("设备令牌");
    API配置.设备令牌 = "";
    是否首次运行 = true;
}

/**
 * 打开小红书应用
 */
function 打开小红书() {
    console.log("打开小红书应用");
    let 应用包名 = "com.xingin.xhs";
    try {
        // 优先尝试ROOT模式下am命令启动
        if (DeviceOperation.获取交互操作模式() === 3) { // ROOT模式
            console.log("当前为ROOT模式，尝试am命令启动应用");
            let result = shell("am start -n com.xingin.xhs/.SplashActivity", true);
            等待(1200);
            if (typeof currentPackage === "function" && currentPackage() === 应用包名) {
                console.log("am命令后检测到小红书已在前台，视为成功");
                return true;
            } else {
                console.warn("am命令启动后未检测到小红书，尝试monkey命令");
                let monkeyResult = shell('monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1', true);
                等待(1500);
                if (typeof currentPackage === "function" && currentPackage() === 应用包名) {
                    console.log("monkey命令后检测到小红书已在前台，视为成功");
                    return true;
                } else {
                    console.error("monkey命令后小红书未在前台: " + monkeyResult.error);
                    return false;
                }
            }
        }
        // 非ROOT模式也用monkey命令
        console.log("非ROOT模式，直接用monkey命令启动");
        let monkeyResult = shell('monkey -p com.xingin.xhs -c android.intent.category.LAUNCHER 1', true);
        等待(1500);
        if (typeof currentPackage === "function" && currentPackage() === 应用包名) {
            return true;
        } else {
            console.error("monkey命令启动失败: " + monkeyResult.error);
            return false;
        }
    } catch (e) {
        console.error("打开小红书失败: " + e.message);
        return false;
    }
}

/**
 * 返回主界面
 * 多次按返回键，直到回到主界面
 * 优化版：减少查询次数和等待时间，兼容ROOT模式
 * 
 * @returns {boolean} - 是否成功返回主界面
 */
function 返回主界面() {
    console.log("返回主界面");

    // 首页可能出现的关键词
    const 首页关键词 = ["首页", "发现", "关注", "直播", "短剧", "附近", "热门", "消息", "我", "推荐", "穿搭", "情感"];

    // 最多按5次返回键
    for (let i = 0; i < 5; i++) {
        // 检查是否已在主界面
        let 找到的关键词 = 0;

        try {
            // 根据操作模式选择不同的检查方法
            if (DeviceOperation.获取交互操作模式() === 3) {  // ROOT模式
                // 使用DeviceOperation的方法获取XML并解析
                let pageXml = DeviceOperation.获取窗口XML();
                if (pageXml) {
                    // 检查有多少关键词存在于XML中
                    for (let 关键词 of 首页关键词) {
                        if (pageXml.includes(`text="${关键词}"`) ||
                            pageXml.includes(`text="${关键词} "`) ||
                            pageXml.includes(`"${关键词}"`) ||
                            pageXml.includes(`content-desc="${关键词}"`)) {
                            找到的关键词++;

                            // 如果找到3个或以上关键词，认为已在首页
                            if (找到的关键词 >= 3) {
                                console.log(`ROOT模式下已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                                return true;
                            }
                        }
                    }
                }
            } else {  // 无障碍模式
                // 查找所有文本元素，减少超时时间
                let 所有文本 = textMatches(".*").find();

                // 检查有多少关键词存在
                for (let j = 0; j < 所有文本.length; j++) {
                    let 文本内容 = 所有文本[j].text();
                    if (!文本内容) continue;

                    // 使用some方法更高效地检查是否包含任何关键词
                    if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                        找到的关键词++;

                        // 如果找到3个或以上关键词，认为已在首页
                        if (找到的关键词 >= 3) {
                            console.log(`无障碍模式下已找到 ${找到的关键词} 个首页关键词，确认在首页`);
                            return true;
                        }
                    }
                }
            }
        } catch (e) {
            console.error("检查首页元素出错: " + e.message);
        }

        // 按返回键
        console.log("按返回键");

        // 检查当前交互模式
        if (DeviceOperation.获取交互操作模式() === 3) {
            // ROOT模式：使用设备操作模块的返回方法
            console.log("使用ROOT模式执行返回操作");
            DeviceOperation.返回();
        } else {
            // 无障碍模式：使用系统back函数
            console.log("使用无障碍模式执行返回操作");
            back();
        }

        等待(1000);
    }

    // 最后检查一次
    let 找到的关键词 = 0;

    if (DeviceOperation.获取交互操作模式() === 3) {  // ROOT模式
        let pageXml = DeviceOperation.获取窗口XML();
        if (pageXml) {
            for (let 关键词 of 首页关键词) {
                if (pageXml.includes(`text="${关键词}"`) ||
                    pageXml.includes(`text="${关键词} "`) ||
                    pageXml.includes(`"${关键词}"`) ||
                    pageXml.includes(`content-desc="${关键词}"`)) {
                    找到的关键词++;
                }
            }
        }
    } else {  // 无障碍模式
        let 所有文本 = textMatches(".*").find();
        for (let j = 0; j < 所有文本.length; j++) {
            let 文本内容 = 所有文本[j].text();
            if (!文本内容) continue;

            if (首页关键词.some(关键词 => 文本内容.includes(关键词))) {
                找到的关键词++;
            }
        }
    }

    if (找到的关键词 >= 3) {
        console.log(`已找到 ${找到的关键词} 个首页关键词，确认在首页`);
        return true;
    }

    console.log("未能确认返回首页");
    return false;
}

/**
 * 处理权限弹窗（自动分发）
 * 检查并点击各种常见的权限按钮
 * 
 * @returns {boolean} - 是否找到并点击了权限按钮
 */
function 处理权限弹窗() {
    console.log("检查权限弹窗...");
    const 权限按钮文本列表 = ["允许", "始终允许", "确定", "继续", "同意", "确认", "好的"];
    if (DeviceOperation.获取交互操作模式() === 3) {
        let pageXml = DeviceOperation.获取窗口XML();
        if (!pageXml) return false;
        for (let 按钮文本 of 权限按钮文本列表) {
            if (pageXml.includes(按钮文本)) {
                // 使用从XML中查找元素方法
                let 元素 = DeviceOperation.从XML中查找元素(pageXml, { text: 按钮文本 }, false);
                if (元素) {
                    let bounds = DeviceOperation.获取元素bounds(元素);
                    if (bounds) {
                        let x = (bounds.left + bounds.right) / 2;
                        let y = (bounds.top + bounds.bottom) / 2;
                        let 点击结果 = DeviceOperation.点击(x, y);
                        if (点击结果) {
                            console.log(`已点击 "${按钮文本}" 按钮`);
                            return true;
                        }
                    }
                }
            }
        }
        return false;
    } else if (DeviceOperation.获取交互操作模式() === 1) {
        for (let i = 0; i < 权限按钮文本列表.length; i++) {
            let 按钮文本 = 权限按钮文本列表[i];
            let 按钮 = text(按钮文本).findOne(1000);
            if (按钮 && 按钮.clickable()) {
                按钮.click();
                sleep(500);
                return true;
            }
        }
        for (let i = 0; i < 权限按钮文本列表.length; i++) {
            let 按钮文本 = 权限按钮文本列表[i];
            let 元素 = textContains(按钮文本).findOne(500);
            if (元素) {
                元素.click();
                sleep(500);
                return true;
            }
        }
        return false;
    } else {
        console.error("未知操作模式，无法处理权限弹窗");
        return false;
    }
}

/**
 * 检查账号异常提示（自动分发）
 * 检测是否出现需要更换账号的提示，如账号下线、登录过期等
 * 优化版本：减少查询次数，提高执行效率
 * 
 * @returns {boolean} - 是否检测到账号异常提示
 */
function 检查账号异常提示() {
    console.log("检查账号异常提示...");
    try {
        let 异常关键词 = ["账号下线", "下线提示", "登录过期", "请重新登录", "其他设备登录", "账号已被冻结", "账号异常", "违反社区规定"];
        if (DeviceOperation.获取交互操作模式() === 3) {
            let pageXml = DeviceOperation.获取窗口XML();
            if (!pageXml) return false;

            // 改进ROOT模式下的文本匹配逻辑
            // 1. 检查text属性
            for (let k of 异常关键词) {
                if (pageXml.includes(`text="${k}"`) ||
                    pageXml.includes(`text="${k} "`) ||
                    pageXml.includes(`text=" ${k}"`) ||
                    pageXml.includes(`text=" ${k} "`)) {
                    console.log(`检测到账号异常提示文本: ${k}`);
                    return true;
                }
            }

            // 2. 检查content-desc属性
            for (let k of 异常关键词) {
                if (pageXml.includes(`content-desc="${k}"`) ||
                    pageXml.includes(`content-desc="${k} "`) ||
                    pageXml.includes(`content-desc=" ${k}"`) ||
                    pageXml.includes(`content-desc=" ${k} "`)) {
                    console.log(`检测到账号异常提示描述: ${k}`);
                    return true;
                }
            }

            // 3. 检查部分匹配
            for (let k of 异常关键词) {
                if (pageXml.includes(k)) {
                    // 再次验证是否在文本内容中
                    let textMatch = pageXml.match(new RegExp(`text="[^"]*${k}[^"]*"`));
                    let descMatch = pageXml.match(new RegExp(`content-desc="[^"]*${k}[^"]*"`));
                    if (textMatch || descMatch) {
                        console.log(`检测到账号异常提示(部分匹配): ${k}`);
                        return true;
                    }
                }
            }

            return false;
        } else if (DeviceOperation.获取交互操作模式() === 1) {
            let 所有文本元素 = textMatches(".*").find();
            for (let i = 0; i < 所有文本元素.length; i++) {
                let 文本内容 = 所有文本元素[i].text();
                if (!文本内容) continue;
                for (let j = 0; j < 异常关键词.length; j++) {
                    if (文本内容.includes(异常关键词[j])) {
                        console.log(`检测到账号异常提示: "${文本内容}" 包含关键词 "${异常关键词[j]}"`);
                        return true;
                    }
                }
            }
            return false;
        } else {
            console.error("未知操作模式，无法检查账号异常");
            return false;
        }
    } catch (e) {
        console.error("检查账号异常提示出错: " + e.message);
        return false;
    }
}

/**
 * 打开链接
 * 
 * @param {string} 链接 - 要打开的链接
 * @returns {boolean} - 是否成功打开
 */
function 打开链接(链接) {
    console.log("使用浏览器打开链接: " + 链接);

    // 使用通用方式打开链接，不依赖无障碍权限
    try {
        app.openUrl(链接);
        toast("已用浏览器打开链接");
    } catch (e) {
        return false;
    }

    const 最大尝试次数 = 20;
    const 每次尝试间隔 = 1000; // 2秒
    const 总超时时间 = 50000; // 50秒
    let 开始时间 = new Date().getTime();

    for (let i = 0; i < 最大尝试次数; i++) {
        if (new Date().getTime() - 开始时间 > 总超时时间) {
            console.warn("OCR识别超时，退出检测");
            break;
        }

        // 首先检查是否已进入小红书
        if (DeviceOperation.检查是否进入小红书()) {
            console.log("已成功跳转到小红书App");
            return true;
        }

        console.log(`第${i + 1}次OCR识别页面...`);
        let 识别内容 = "*App内打开*|*Chrome*|*允许*|同意";
        // OCR识别全屏文字
        let 结果 = null;
        try {
            // 增加错误处理和重试
            let 重试次数 = 0;
            const 最大OCR重试 = 3;

            while (重试次数 < 最大OCR重试) {
                try {
                    结果 = OCR.获取屏幕文字信息("", 识别内容, 1, "", true);
                    if (结果 && 结果.length > 0) {
                        OCR.点击指定关键词(结果, 识别内容);

                        // 点击后等待1秒再检查是否进入小红书
                        sleep(1000);
                        break; // 成功则跳出重试循环
                    }
                } catch (ocrError) {
                    重试次数++;
                    console.error(`OCR识别失败 (${重试次数}/${最大OCR重试}): ${ocrError.message}`);
                    if (重试次数 < 最大OCR重试) {
                        console.log("等待1秒后重试OCR识别...");
                        sleep(1000);
                    } else {
                        console.error("OCR识别重试次数已达上限，跳过本次识别");
                        结果 = null;
                    }
                }
                if (DeviceOperation.检查是否进入小红书()) {
                    console.log("2已成功跳转到小红书App");
                    return true;
                }
            }
        } catch (e) {
            console.error("OCR识别出错: " + e.message);
            // 继续下一次循环，不中断整个流程
        }

        sleep(每次尝试间隔);
    }

    console.warn("未识别到'App内打开'按钮或点击失败");
    return false;
}





/**
 * 获取特定配置项的值
 * 
 * @param {string} 配置名称 - 配置项名称
 * @param {any} 默认值 - 如果配置不存在，返回的默认值
 * @returns {any} - 配置项的值
 */
function 获取配置项(配置名称, 默认值) {
    // 先尝试直接从存储中获取
    let storage = storages.create("小红书操作");
    let 配置值 = storage.get(配置名称);

    // 如果直接存储中有值，直接返回
    if (配置值 !== undefined && 配置值 !== null) {
        console.log(`获取配置项 ${配置名称}: ${配置值}`);
        return 配置值;
    }

    // 尝试从所有配置中获取
    let 所有配置 = storage.get("所有配置");
    if (所有配置) {
        // 遍历所有配置项
        for (let 配置组名 in 所有配置) {
            let 配置组 = 所有配置[配置组名];
            // 如果配置组中包含指定的配置项
            if (配置组 && 配置组[配置名称] !== undefined) {
                console.log(`从配置组 ${配置组名} 中获取配置项 ${配置名称}: ${配置组[配置名称]}`);
                return 配置组[配置名称];
            }
        }
    }

    // 尝试从当前配置中获取
    let 当前配置 = storage.get("当前配置");
    if (当前配置 && 当前配置.config && 当前配置.config[配置名称] !== undefined) {
        console.log(`从当前配置中获取配置项 ${配置名称}: ${当前配置.config[配置名称]}`);
        return 当前配置.config[配置名称];
    }

    // 如果都没有找到，返回默认值
    console.log(`未找到配置项 ${配置名称}，使用默认值: ${默认值}`);
    return 默认值;
}

/**
 * 点击首篇文章
 * 
 * @returns {boolean} - 是否成功点击
 */
function 点击首篇文章() {
    console.log("点击首页文章");

    try {
        // 等待首页加载
        //等待(2000);

        // 随机化点击坐标
        let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
        let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20

        console.log(`点击坐标: (${x}, ${y})`);

        // 检查当前交互模式
        if (DeviceOperation.获取交互操作模式() === 3) {
            // ROOT模式：使用设备操作模块的点击方法
            console.log("使用ROOT模式点击首页文章");
            let result = DeviceOperation.点击(x, y);
            if (!result || !result.成功) {
                console.error("ROOT模式点击失败: " + (result ? result.信息 : "未知错误"));
                throw new Error("ROOT模式点击失败，请检查ROOT权限是否正确配置");
            }
        } else {
            // 无障碍模式：使用系统click函数
            console.log("使用无障碍模式点击首页文章");
            click(x, y);
        }

        

        return true;
    } catch (e) {
        console.error("点击首篇文章出错: " + e.message);
        return false;
    }
}

/**
 * 获取页面信息（自动分发）
 * 获取当前页面的标题、内容、点赞数、收藏数等信息
 * 
 * @returns {Object|null} - 页面信息，失败返回null
 */
function 获取页面信息() {
    try {
        if (当前为ROOT模式()) {
            console.log("ROOT模式下获取页面信息");
            return 使用ROOT获取页面信息();
        } else if (当前为无障碍模式()) {
            console.log("无障碍模式下获取页面信息");
            return 使用无障碍获取页面信息();
        } else {
            console.error("未知操作模式，无法获取页面信息");
            return null;
        }
    } catch (e) {
        console.error("获取页面信息出错: " + e.message);
        return null;
    }
}

/**
 * 使用ROOT模式获取页面信息
 * 
 * @returns {Object|null} - 页面信息
 */
function 使用ROOT获取页面信息() {
    try {
        // 获取当前窗口XML
        let pageXml = DeviceOperation.获取窗口XML();
        if (!pageXml) {
            console.error("获取页面XML失败");
            return null;
        }

        console.log("成功获取窗口XML，开始解析页面信息");

        // 初始化返回结果
        let 页面信息 = {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            评论数: null,
            已点赞: false,
            已收藏: false,
            是否视频: false,
            内容类型: "图文",
            最长文本列表: [] // 添加最长文本列表字段
        };

        // 1. 解析用户名 - 多种方式查找
        console.log("开始尝试提取用户名...");
        
        // 方式1: 查找包含"作者"的content-desc属性
        let 作者匹配 = pageXml.match(/content-desc="作者([^"]+)"/);
        if (作者匹配) {
            页面信息.用户名 = 作者匹配[1].trim();
            console.log(`找到用户名(方式1-作者前缀): ${页面信息.用户名}`);
        } else {
            console.log("方式1未找到用户名");
        }

        // 方式2: 查找包含"作者"开头的Button元素
        if (!页面信息.用户名) {
            let 作者按钮匹配 = pageXml.match(/Button[^>]*content-desc="作者([^"]+)"/);
            if (作者按钮匹配) {
                页面信息.用户名 = 作者按钮匹配[1].trim();
                console.log(`找到用户名(方式2-作者按钮): ${页面信息.用户名}`);
            } else {
                console.log("方式2未找到用户名");
            }
        }
        
        // 方式3: 查找带有"作者"前缀但逗号分隔的情况
        if (!页面信息.用户名) {
            let 作者逗号匹配 = pageXml.match(/content-desc="作者,([^"]+)"/);
            if (作者逗号匹配) {
                页面信息.用户名 = 作者逗号匹配[1].trim();
                console.log(`找到用户名(方式3-作者逗号): ${页面信息.用户名}`);
            } else {
                console.log("方式3未找到用户名");
            }
        }

        // 方式4: 查找nickNameTV元素
        if (!页面信息.用户名) {
            let nickNameTV匹配 = pageXml.match(/resource-id="com\.xingin\.xhs:id\/nickNameTV"[^>]*text="([^"]+)"/);
            if (nickNameTV匹配) {
                页面信息.用户名 = nickNameTV匹配[1].trim();
                console.log(`找到用户名(方式4-nickNameTV): ${页面信息.用户名}`);
            } else {
                // 输出更多调试信息
                console.log("方式4未找到用户名，尝试查找nickNameTV元素");
                let nickNameTV检查 = pageXml.match(/nickNameTV/g);
                if (nickNameTV检查) {
                    console.log(`找到 ${nickNameTV检查.length} 个nickNameTV元素`);
                } else {
                    console.log("未找到nickNameTV元素");
                }
            }
        }

        // 方式5: 查找matrixNickNameView元素
        if (!页面信息.用户名) {
            let 昵称匹配 = pageXml.match(/resource-id="com\.xingin\.xhs:id\/matrixNickNameView"[^>]*text="([^"]+)"/);
            if (昵称匹配) {
                页面信息.用户名 = 昵称匹配[1].trim();
                console.log(`找到用户名(方式5-matrixNickNameView): ${页面信息.用户名}`);
            } else {
                console.log("方式5未找到用户名");
            }
        }
        
        // 方式6: 专门处理"作者XX的日记"格式 - 获取完整昵称
        if (!页面信息.用户名) {
            let 日记匹配 = pageXml.match(/Button[^>]*content-desc="作者([^"]+)"/);
            if (日记匹配) {
                页面信息.用户名 = 日记匹配[1].trim();
                console.log(`找到用户名(方式6-日记格式): ${页面信息.用户名}`);
            } else {
                console.log("方式6未找到用户名");
            }
        }
        
        // 方式7: 通用匹配，如果上面都没匹配到
        if (!页面信息.用户名) {
            let 通用匹配 = pageXml.match(/content-desc="作者([^"]+)"/);
            if (通用匹配) {
                页面信息.用户名 = 通用匹配[1].trim();
                console.log(`找到用户名(方式7-通用匹配): ${页面信息.用户名}`);
            } else {
                console.log("方式7未找到用户名");
            }
        }
        
        // 方式8: 直接从提取的文本中寻找可能的用户名
        if (!页面信息.用户名) {
            // 收集所有可能的文本内容
            let 所有文本内容 = [];
            
            // 提取所有text属性中的文本
            const textPattern8 = /text=['"]([^'"]+)['"]/g;
            let textMatch;
            while ((textMatch = textPattern8.exec(pageXml)) !== null) {
                let 文本 = textMatch[1].trim();
                if (文本.length > 0) {
                    所有文本内容.push(文本);
                }
            }
            
            // 查找nickNameTV元素的文本
            for (let i = 0; i < 所有文本内容.length; i++) {
                let 文本 = 所有文本内容[i];
                // 检查是否是用户名的可能性高的文本
                if (文本.length > 1 && 文本.length < 20 && !文本.includes("#") && !文本.includes("http")) {
                    // 检查是否在nickNameTV元素附近
                    let 前后文本 = pageXml.substring(Math.max(0, pageXml.indexOf(文本) - 100), 
                                              Math.min(pageXml.length, pageXml.indexOf(文本) + 100));
                    if (前后文本.includes("nickNameTV") || 前后文本.includes("NickName")) {
                        页面信息.用户名 = 文本;
                        console.log(`找到用户名(方式8-文本分析): ${页面信息.用户名}`);
                        break;
                    }
                }
            }
            
            // 如果还是没找到，尝试直接使用第一个短文本
            if (!页面信息.用户名 && 所有文本内容.length > 0) {
                // 过滤掉明显不是用户名的文本
                let 可能的用户名 = 所有文本内容.filter(文本 => 
                    文本.length > 1 && 文本.length < 20 && 
                    !文本.includes("#") && 
                    !文本.includes("http") &&
                    !文本.match(/^(关注|点赞|收藏|评论|分享|举报|送礼物|说点什么)/)
                );
                
                if (可能的用户名.length > 0) {
                    页面信息.用户名 = 可能的用户名[0];
                    console.log(`找到可能的用户名(方式8-首个短文本): ${页面信息.用户名}`);
                } else {
                    console.log("方式8未找到用户名");
                }
            }
        }
        
        // 如果仍然没有找到用户名，输出警告
        if (!页面信息.用户名) {
            console.warn("警告：未能提取到用户名");
            // 输出XML的前500个字符，帮助调试
            console.log("XML前500字符: " + pageXml.substring(0, 500));
        }

        // 2. 收集所有可能的文本内容
        let 所有文本内容 = [];

        // 2.1 提取所有text属性中的文本
        const textPattern = /text=['"]([^'"]+)['"]/g;
        let textMatch;
        while ((textMatch = textPattern.exec(pageXml)) !== null) {
            let 文本 = textMatch[1].trim();
            if (文本.length > 5) { // 只收集长度大于5的文本
                // 过滤掉明显是功能按钮的文本
                if (!文本.match(/^(关注|点赞|收藏|评论|分享|举报|送礼物|说点什么)/)) {
                    所有文本内容.push(文本);
                    console.log(`[文本提取] 从text属性提取内容: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""}`);
                }
            }
        }

        // 2.2 提取所有content-desc属性中的文本
        const descPattern = /content-desc=['"]([^'"]+)['"]/g;
        let descMatch;
        while ((descMatch = descPattern.exec(pageXml)) !== null) {
            let 文本 = descMatch[1].trim();
            if (文本.length > 5 && !文本.startsWith("作者,") && !文本.startsWith("点赞") && !文本.startsWith("收藏") && !文本.startsWith("评论")) {
                所有文本内容.push(文本);
                console.log(`[文本提取] 从content-desc属性提取内容: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""}`);
            }
        }

        // 2.3 专门提取长文本节点
        // 针对小红书文章正文，通常在长TextView元素中
        const longTextViewPattern = /<node [^>]*class="android\.widget\.TextView"[^>]*text='([^']{100,})'[^>]*\/>/g;
        let longTextMatch;
        while ((longTextMatch = longTextViewPattern.exec(pageXml)) !== null) {
            let 文本 = longTextMatch[1].trim();
            所有文本内容.push(文本);
            console.log(`[文本提取] 从长TextView提取内容: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""} (${文本.length}字)`);
        }

        // 2.4 处理双引号包含的长文本
        const longTextViewPattern2 = /<node [^>]*class="android\.widget\.TextView"[^>]*text="([^"]{100,})"[^>]*\/>/g;
        let longTextMatch2;
        while ((longTextMatch2 = longTextViewPattern2.exec(pageXml)) !== null) {
            let 文本 = longTextMatch2[1].trim();
            所有文本内容.push(文本);
            console.log(`[文本提取] 从长TextView(双引号)提取内容: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""} (${文本.length}字)`);
        }

        // 3. 对文本内容进行处理和排序
        // 3.1 过滤掉重复内容
        let 唯一文本内容 = 所有文本内容.filter((value, index, self) => {
            return self.indexOf(value) === index;
        });

        // 3.2 按长度排序
        唯一文本内容.sort((a, b) => b.length - a.length);

        // 3.3 取最长的3段文本
        页面信息.最长文本列表 = 唯一文本内容.slice(0, 3);

        // 输出调试信息
        console.log(`[文本统计] 共提取到 ${唯一文本内容.length} 段文本内容`);
        for (let i = 0; i < 页面信息.最长文本列表.length; i++) {
            let 文本 = 页面信息.最长文本列表[i];
            let 显示文本 = 文本.length > 100 ? 文本.substring(0, 100) + "..." : 文本;
            console.log(`[长文本${i + 1}] (${文本.length}字): ${显示文本}`);
        }

        // 4. 为了兼容性，将最长文本设置为标题和内容
        if (页面信息.最长文本列表.length > 0) {
            页面信息.标题 = 页面信息.最长文本列表[0];

            if (页面信息.最长文本列表.length > 1) {
                页面信息.内容 = 页面信息.最长文本列表[1];
            }
        }

        // 5. 解析互动数据
        // 5.1 检查右侧互动区域
        let 互动区域XML = pageXml.match(/class="android\.widget\.LinearLayout"[^>]*bounds="\[927,\d+\]\[1080,\d+\]"[^>]*>([\s\S]*?)<\/node>/);

        if (互动区域XML) {
            // 解析点赞数据
            let 点赞匹配 = 互动区域XML[1].match(/content-desc="(已点赞|点赞)\s*(\d*)"/);
            if (点赞匹配) {
                页面信息.已点赞 = 点赞匹配[1] === "已点赞";
                页面信息.点赞数 = 点赞匹配[2] ? parseInt(点赞匹配[2]) : (页面信息.已点赞 ? 1 : 0);
            }

            // 解析收藏数据
            let 收藏匹配 = 互动区域XML[1].match(/content-desc="(已收藏|收藏)\s*(\d*)"/);
            if (收藏匹配) {
                页面信息.已收藏 = 收藏匹配[1] === "已收藏";
                页面信息.收藏数 = 收藏匹配[2] ? parseInt(收藏匹配[2]) : (页面信息.已收藏 ? 1 : 0);
            }

            // 解析评论数据
            let 评论匹配 = 互动区域XML[1].match(/content-desc="评论\s*(\d*)"/);
            if (评论匹配) {
                页面信息.评论数 = 评论匹配[1] ? parseInt(评论匹配[1]) : 0;
            }
        } else {
            // 5.2 如果没有找到右侧互动区域，尝试从底部区域查找
            console.log("右侧互动区域未找到数据，尝试底部互动区域");

            // 查找点赞数据
            let 底部点赞匹配 = pageXml.match(/content-desc="(已点赞|点赞)\s*(\d*)"/);
            if (底部点赞匹配) {
                页面信息.已点赞 = 底部点赞匹配[1] === "已点赞";
                页面信息.点赞数 = 底部点赞匹配[2] ? parseInt(底部点赞匹配[2]) : (页面信息.已点赞 ? 1 : 0);
                console.log(`从底部区域找到点赞信息: ${页面信息.已点赞 ? "已点赞" : "未点赞"} ${页面信息.点赞数}`);
            }

            // 查找收藏数据
            let 底部收藏匹配 = pageXml.match(/content-desc="(已收藏|收藏)\s*(\d*)"/);
            if (底部收藏匹配) {
                页面信息.已收藏 = 底部收藏匹配[1] === "已收藏";
                页面信息.收藏数 = 底部收藏匹配[2] ? parseInt(底部收藏匹配[2]) : (页面信息.已收藏 ? 1 : 0);
                console.log(`从底部区域找到收藏信息: ${页面信息.已收藏 ? "已收藏" : "未收藏"} ${页面信息.收藏数}`);
            }

            // 查找评论数据
            let 底部评论匹配 = pageXml.match(/content-desc="评论\s*(\d*)"/);
            if (底部评论匹配) {
                页面信息.评论数 = 底部评论匹配[1] ? parseInt(底部评论匹配[1]) : 0;
                console.log(`从底部区域找到评论数: ${页面信息.评论数}`);
            }
        }

        // 6. 判断是否为视频
        if (pageXml.includes('content-desc="暂停"') ||
            pageXml.includes('VideoSeekBar') ||
            pageXml.includes('已播放到') ||
            pageXml.includes('剩余时间') ||
            pageXml.match(/共\d+分\d+秒/)) {
            页面信息.是否视频 = true;
            页面信息.内容类型 = "视频";
            console.log("检测到视频内容");
        }

        console.log("页面信息解析完成");
        return 页面信息;
    } catch (e) {
        console.error("使用ROOT获取页面信息出错: " + e.message);
        return null;
    }
}

/**
 * 使用无障碍服务获取页面信息
 * 
 * @returns {Object|null} - 页面信息
 */
function 使用无障碍获取页面信息() {
    try {
        // 初始化返回结果
        let 页面信息 = {
            标题: null,
            内容: null,
            用户名: null,
            点赞数: null,
            收藏数: null,
            评论数: null,
            已点赞: false,
            已收藏: false,
            是否视频: false,
            内容类型: "图文",
            最长文本列表: [] // 添加最长文本列表字段
        };

        // 1. 检查是否为视频页面
        let 视频指示器 = textMatches("已播放到.*").findOne(500) ||
            descMatches("已播放到.*").findOne(500) ||
            descMatches("暂停").findOne(500) ||
            className("com.xingin.redview.seekbar.VideoSeekBar").findOne(500);

        let 是否视频页面 = 视频指示器 != null;
        页面信息.是否视频 = 是否视频页面;
        页面信息.内容类型 = 是否视频页面 ? "视频" : "图文";

        // 2. 获取用户名
        // 方法1: 从作者按钮获取
        let 作者按钮 = descMatches("作者.*").findOne(500);
        if (作者按钮) {
            let desc = 作者按钮.desc();
            if (desc && desc.startsWith("作者,")) {
                页面信息.用户名 = desc.substring(3);
                console.log(`从作者按钮找到用户名: ${页面信息.用户名}`);
            }
        }

        // 方法2: 从matrixNickNameView获取
        if (!页面信息.用户名) {
            let 昵称节点 = id("matrixNickNameView").findOne(500);
            if (昵称节点) {
                页面信息.用户名 = 昵称节点.text();
                console.log(`从昵称节点找到用户名: ${页面信息.用户名}`);
            }
        }

        // 3. 收集所有可能的文本内容
        let 所有文本内容 = [];

        // 3.1 收集所有文本元素的text属性
        let 所有文本元素 = textMatches(".+").find();
        for (let i = 0; i < 所有文本元素.length; i++) {
            let elem = 所有文本元素[i];
            let 文本 = elem.text();

            if (!文本 || 文本.trim().length < 5) continue; // 忽略太短的文本

            // 过滤掉功能按钮文本
            if (!文本.match(/^(关注|点赞|收藏|评论|分享|举报|送礼物|说点什么)/)) {
                所有文本内容.push(文本.trim());
                console.log(`[文本提取] 从text属性提取内容: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""}`);
            }
        }

        // 3.2 收集所有元素的desc属性
        let 所有描述元素 = descMatches(".+").find();
        for (let i = 0; i < 所有描述元素.length; i++) {
            let elem = 所有描述元素[i];
            let 描述 = elem.desc();

            if (!描述 || 描述.trim().length < 5) continue; // 忽略太短的描述

            // 过滤掉功能按钮和已处理的描述
            if (!描述.startsWith("作者,") && !描述.match(/^(点赞|收藏|评论|分享)/)) {
                所有文本内容.push(描述.trim());
                console.log(`[文本提取] 从desc属性提取内容: ${描述.substring(0, 50)}${描述.length > 50 ? "..." : ""}`);
            }
        }

        // 3.3 专门查找长文本节点 - 从小红书ID为0_resource_name_obfuscated的TextView中获取
        let 所有混淆ID文本元素 = className("android.widget.TextView").filter(function (element) {
            let id = element.id();
            return id && id.startsWith("com.xingin.xhs:id/0_resource_name_obfuscated");
        }).find();

        for (let i = 0; i < 所有混淆ID文本元素.length; i++) {
            let elem = 所有混淆ID文本元素[i];
            let 文本 = elem.text();

            if (!文本 || 文本.trim().length < 5) continue; // 忽略太短的文本

            console.log(`[文本提取] 从混淆ID TextView提取内容: ${文本.substring(0, 50)}${文本.length > 50 ? "..." : ""} (${文本.length}字)`);
            所有文本内容.push(文本.trim());
        }

        // 4. 处理收集到的文本
        // 4.1 去重
        let 唯一文本内容 = 所有文本内容.filter((value, index, self) => {
            return self.indexOf(value) === index;
        });

        // 4.2 按长度排序
        唯一文本内容.sort((a, b) => b.length - a.length);

        // 4.3 取最长的3段文本
        页面信息.最长文本列表 = 唯一文本内容.slice(0, 3);

        // 输出调试信息
        console.log(`[文本统计] 共提取到 ${唯一文本内容.length} 段文本内容`);
        for (let i = 0; i < 页面信息.最长文本列表.length; i++) {
            let 文本 = 页面信息.最长文本列表[i];
            let 显示文本 = 文本.length > 100 ? 文本.substring(0, 100) + "..." : 文本;
            console.log(`[长文本${i + 1}] (${文本.length}字): ${显示文本}`);
        }

        // 5. 为了兼容性，将最长文本设置为标题和内容
        if (页面信息.最长文本列表.length > 0) {
            页面信息.标题 = 页面信息.最长文本列表[0];

            if (页面信息.最长文本列表.length > 1) {
                页面信息.内容 = 页面信息.最长文本列表[1];
            }
        }

        // 6. 获取互动数据
        // 6.1 查找点赞元素
        let 点赞元素 = descMatches(".*点赞.*").findOne(500);
        if (点赞元素) {
            let desc = 点赞元素.desc();
            页面信息.已点赞 = desc.includes("已点赞");
            // 提取点赞数
            if (desc === "点赞") {
                页面信息.点赞数 = 0;
            } else if (desc === "已点赞") {
                页面信息.点赞数 = 1; // 已点赞时数量至少为1
            } else {
                页面信息.点赞数 = 提取数字(desc);
            }
            console.log(`找到点赞信息: ${页面信息.已点赞 ? "已点赞" : "未点赞"} ${页面信息.点赞数}`);
        }

        // 6.2 查找收藏元素
        let 收藏元素 = descMatches(".*收藏.*").findOne(500);
        if (收藏元素) {
            let desc = 收藏元素.desc();
            页面信息.已收藏 = desc.includes("已收藏");
            // 提取收藏数
            if (desc === "收藏") {
                页面信息.收藏数 = 0;
            } else if (desc === "已收藏") {
                页面信息.收藏数 = 1; // 已收藏时数量至少为1
            } else {
                页面信息.收藏数 = 提取数字(desc);
            }
            console.log(`找到收藏信息: ${页面信息.已收藏 ? "已收藏" : "未收藏"} ${页面信息.收藏数}`);
        }

        // 6.3 查找评论元素
        let 评论元素 = descMatches(".*评论.*").findOne(500);
        if (评论元素) {
            let desc = 评论元素.desc();
            if (desc === "评论") {
                页面信息.评论数 = 0;
            } else {
                页面信息.评论数 = 提取数字(desc);
            }
            console.log(`找到评论数: ${页面信息.评论数}`);
        }

        console.log("页面信息获取完成");
        return 页面信息;
    } catch (e) {
        console.error("使用无障碍获取页面信息出错: " + e.message);
        return null;
    }
}

// 正确导出流程函数
module.exports = {
    开始小红书点赞操作,
    智能自动操作,
    设备注册,
    获取下一个链接,
    更新操作状态,
    获取操作信息,
    获取当前配置,
    获取设备令牌,
    更新设备令牌,
    清除设备令牌,
    设置API配置,
    获取互动元素,
    使用ROOT获取互动元素,
    使用无障碍获取互动元素,
    提取数字,
    执行点赞,
    执行收藏,
    打开链接,
    打开小红书,
    返回主界面,
    检查账号异常提示,
    处理权限弹窗,
    获取配置项,
    等待,
    随机等待,
    点击首篇文章,
    获取页面信息,
    使用ROOT获取页面信息,
    使用无障碍获取页面信息,
    比较页面信息
};

/**
 * 智能自动操作函数
 * 包含返回首页、点击首篇文章、比较文章信息等完整流程
 * 
 * @param {number} 最大操作次数 - 最大操作次数，0表示不限制
 * @returns {Object} - 操作结果
 */
function 智能自动操作(最大操作次数 = 0) {
    console.log("开始智能自动操作");

    // 获取操作信息
    let 操作信息 = 获取操作信息();
    if (!操作信息 || !操作信息.链接) {
        console.log("没有可操作的链接");
        return { success: false, message: "没有可操作的链接" };
    }

    let 链接 = 操作信息.链接;
    let 链接ID = 操作信息.链接ID;
    let 任务ID = 操作信息.任务ID;
    let 原始点赞数 = 操作信息.原始点赞数 || 0;
    let 原始收藏数 = 操作信息.原始收藏数 || 0;
    let 目标点赞数 = 操作信息.目标点赞数 || 0;
    let 目标收藏数 = 操作信息.目标收藏数 || 0;

    console.log(`开始处理链接: ${链接}`);
    console.log(`原始数据 - 点赞: ${原始点赞数}, 收藏: ${原始收藏数}`);
    console.log(`目标数据 - 点赞: ${目标点赞数}, 收藏: ${目标收藏数}`);

    // 打开链接
    if (!打开链接(链接)) {
        console.log("打开链接失败，更新操作状态为失败");
        更新操作状态(链接ID, "failed", "both", 原始点赞数, 原始收藏数, "打开链接失败");
        return { success: false, message: "打开链接失败" };
    }

    // 等待页面加载
    等待(3000);

    // 获取目标页面信息
    console.log("获取目标页面信息...");
    let 目标内容 = 获取页面信息();
    if (!目标内容) {
        console.log("未获取到目标页面信息，可能小红书未打开或未进入正确页面");
        更新操作状态(链接ID, "failed", "both", 0, 0, "未获取到目标页面信息");
        return { success: false, message: "未获取到目标页面信息" };
    }

    console.log("成功获取目标页面信息：", 目标内容.标题 || 目标内容.内容);

    // 记录目标页面的点赞和收藏数
    let 互动元素 = 获取互动元素();
    let 操作前点赞数 = 互动元素 ? (互动元素.点赞 || 0) : 0;
    let 操作前收藏数 = 互动元素 ? (互动元素.收藏 || 0) : 0;
    console.log(`目标页面数据 - 点赞数: ${操作前点赞数}, 收藏数: ${操作前收藏数}`);

    // 返回首页
    console.log("返回首页...");
    if (!返回主界面()) {
        console.log("返回首页失败");
        更新操作状态(链接ID, "failed", "both", 操作前点赞数, 操作前收藏数, "返回首页失败");
        return { success: false, message: "返回首页失败" };
    }

    // 等待首页加载
    等待(2000);

    // 点击首页第一篇文章
    console.log("点击首页第一篇文章...");
    if (!点击首篇文章()) {
        console.log("点击首页第一篇文章失败");
        更新操作状态(链接ID, "failed", "both", 操作前点赞数, 操作前收藏数, "点击首页第一篇文章失败");
        return { success: false, message: "点击首页第一篇文章失败" };
    }

    // 等待文章页面加载
    等待(3000);

    // 获取首页第一篇文章的页面信息
    console.log("获取首页文章信息...");
    let 首页内容 = 获取页面信息();
    if (!首页内容) {
        console.log("未获取到首页文章信息");
        更新操作状态(链接ID, "failed", "both", 操作前点赞数, 操作前收藏数, "未获取到首页文章信息");
        return { success: false, message: "未获取到首页文章信息" };
    }

    console.log("成功获取首页文章信息：", 首页内容.标题 || 首页内容.内容);

    // 比对两次内容
    console.log("比对目标内容和首页内容...");
    if (!比较页面信息(目标内容, 首页内容)) {
        console.log("内容不匹配，不是同一篇文章");
        更新操作状态(链接ID, "failed", "both", 操作前点赞数, 操作前收藏数, "内容不匹配，不是同一篇文章");
        return { success: false, message: "内容不匹配，不是同一篇文章" };
    }

    console.log("内容匹配，确认是同一篇文章，准备执行点赞和收藏操作");

    // 计算需要增加的数量
    let 需要增加点赞数 = 目标点赞数 - (首页内容.点赞数 - 原始点赞数);
    let 需要增加收藏数 = 目标收藏数 - (首页内容.收藏数 - 原始收藏数);

    // 处理初始值为0的特殊情况
    if (原始点赞数 === 0) {
        需要增加点赞数 = 目标点赞数;
    }
    if (原始收藏数 === 0) {
        需要增加收藏数 = 目标收藏数;
    }

    console.log(`需要增加 - 点赞: ${需要增加点赞数}, 收藏: ${需要增加收藏数}`);

    // 检查是否已经达标
    let 点赞已达标 = 需要增加点赞数 <= 0;
    let 收藏已达标 = 需要增加收藏数 <= 0;

    if (点赞已达标 && 收藏已达标) {
        console.log("点赞和收藏均已达到目标数量，无需操作");
        更新操作状态(链接ID, "success", "both", 首页内容.点赞数, 首页内容.收藏数);
        返回主界面();
        return { success: true, message: "点赞和收藏均已达到目标数量，无需操作" };
    }

    // 根据是否达标决定是否执行操作
    let 点赞成功 = false;
    let 收藏成功 = false;

    // 执行点赞操作
    if (!点赞已达标 && 需要增加点赞数 > 0) {
        console.log(`开始执行点赞操作，需要增加 ${需要增加点赞数} 个点赞`);
        点赞成功 = 执行点赞();
        if (点赞成功) {
            console.log("点赞操作成功");
        } else {
            console.log("点赞操作失败");
        }
    } else if (点赞已达标) {
        console.log("点赞已达标，跳过点赞操作");
        点赞成功 = true;
    }

    // 随机等待
    随机等待(1000, 2000);

    // 执行收藏操作
    if (!收藏已达标 && 需要增加收藏数 > 0) {
        console.log(`开始执行收藏操作，需要增加 ${需要增加收藏数} 个收藏`);
        收藏成功 = 执行收藏();
        if (收藏成功) {
            console.log("收藏操作成功");
        } else {
            console.log("收藏操作失败");
        }
    } else if (收藏已达标) {
        console.log("收藏已达标，跳过收藏操作");
        收藏成功 = true;
    }

    // 确定操作类型
    let 操作类型 = "both";
    if (点赞已达标 && !收藏已达标) {
        操作类型 = "收藏";
    } else if (!点赞已达标 && 收藏已达标) {
        操作类型 = "点赞";
    }

    // 更新操作状态
    if (点赞成功 && 收藏成功) {
        console.log("所有操作成功完成");
        更新操作状态(链接ID, "success", 操作类型, 首页内容.点赞数, 首页内容.收藏数);
        返回主界面();
        return { success: true, message: "所有操作成功完成" };
    } else {
        console.log("部分操作失败");
        let 失败信息 = [];
        if (!点赞成功 && !点赞已达标) 失败信息.push("点赞失败");
        if (!收藏成功 && !收藏已达标) 失败信息.push("收藏失败");

        更新操作状态(链接ID, "failed", 操作类型, 首页内容.点赞数, 首页内容.收藏数, 失败信息.join(", "));
        返回主界面();
        return { success: false, message: 失败信息.join(", ") };
    }
}

/**
 * 点击首篇文章
 *
 * @returns {boolean} - 是否成功点击
 */
function 点击首篇文章() {
    console.log("点击首页文章");

    try {
        // 等待首页加载
        //等待(2000);

        // 随机化点击坐标
        let x = 250 + Math.floor(Math.random() * 40) - 20;  // 250 ± 20
        let y = 500 + Math.floor(Math.random() * 40) - 20;  // 500 ± 20

        console.log(`点击坐标: (${x}, ${y})`);

        // 检查当前交互模式
        if (DeviceOperation.获取交互操作模式() === 3) {
            // ROOT模式：使用设备操作模块的点击方法
            console.log("使用ROOT模式点击首页文章");
            let result = DeviceOperation.点击(x, y);
            if (!result || !result.成功) {
                console.error("ROOT模式点击失败: " + (result ? result.信息 : "未知错误"));
                throw new Error("ROOT模式点击失败，请检查ROOT权限是否正确配置");
            }
        } else {
            // 无障碍模式：使用系统click函数
            console.log("使用无障碍模式点击首页文章");
            click(x, y);
        }

        return true;
    } catch (e) {
        console.error("点击首篇文章出错: " + e.message);
        return false;
    }
}

/**
 * 获取界面XML信息
 * 调用DeviceOperation模块获取当前界面的XML信息
 * @returns {string|null} XML字符串，失败返回null
 */
function 获取界面XML信息() {
    console.log("开始获取界面XML信息...");
    try {
        let xml内容 = DeviceOperation.获取窗口XML();
        if (xml内容) {
            console.log(`成功获取XML信息，长度: ${xml内容.length} 字符`);
            return xml内容;
        } else {
            console.error("获取XML信息失败");
            return null;
        }
    } catch (e) {
        console.error("获取界面XML信息出错:", e.message);
        return null;
    }
}

/**
 * 从XML中提取第一个文章的点赞位置坐标
 * 根据小红书界面布局分析，提取第一篇文章的点赞按钮坐标
 * @param {string} xmlContent - XML内容字符串
 * @returns {Object|null} 包含x,y坐标的对象，失败返回null
 */
function 提取第一个点赞位置(xmlContent) {
    console.log("开始提取第一个点赞位置坐标...");

    if (!xmlContent) {
        console.error("XML内容为空");
        return null;
    }

    try {
        // 查找第一个文章元素的bounds信息（使用222.js中验证过的方法）
        let 文章匹配 = null;

        console.log("开始查找第一篇文章...");

        // 方法1：直接查找已知的文章内容
        console.log("方法1：查找包含特定关键词的文章");
        文章匹配 = xmlContent.match(/content-desc="[^"]*贵气千金感妆教[^"]*"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);

        if (文章匹配) {
            console.log("✅ 方法1成功 - 找到'贵气千金感妆教'文章");
        } else {
            console.log("❌ 方法1失败，尝试方法2");

            // 方法2：查找包含"吃草羊仔"的文章
            console.log("方法2：查找特定作者的文章");
            文章匹配 = xmlContent.match(/content-desc="[^"]*吃草羊仔[^"]*"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);

            if (文章匹配) {
                console.log("✅ 方法2成功 - 找到特定作者文章");
            } else {
                console.log("❌ 方法2失败，尝试方法3");

                // 方法3：查找坐标范围合理的大文章
                console.log("方法3：查找坐标范围合理的大文章");
                let allFrameLayouts = xmlContent.match(/class="android\.widget\.FrameLayout"[^>]*content-desc="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/g);

                if (allFrameLayouts) {
                    console.log(`找到 ${allFrameLayouts.length} 个FrameLayout，筛选大文章...`);

                    for (let match of allFrameLayouts) {
                        let detailMatch = match.match(/content-desc="([^"]*)"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
                        if (detailMatch) {
                            let desc = detailMatch[1];
                            let left = parseInt(detailMatch[2]);
                            let top = parseInt(detailMatch[3]);
                            let right = parseInt(detailMatch[4]);
                            let bottom = parseInt(detailMatch[5]);
                            let width = right - left;
                            let height = bottom - top;

                            // 严格筛选条件（与222.js保持一致）：
                            // 1. 左边缘在10-20之间（第一列文章）
                            // 2. 宽度在500-550之间（完整文章宽度）
                            // 3. 高度大于800（完整文章高度）
                            // 4. 包含"来自"和"赞"（确保是文章内容）
                            if (left >= 10 && left <= 20 &&
                                width >= 500 && width <= 550 &&
                                height >= 800 &&
                                desc.includes("来自") && desc.includes("赞")) {

                                console.log(`✅ 方法3成功 - 找到符合条件的大文章:`);
                                console.log(`   内容: ${desc}`);
                                console.log(`   坐标: [${left},${top}][${right},${bottom}]`);
                                console.log(`   尺寸: 宽${width} 高${height}`);
                                文章匹配 = [match, left.toString(), top.toString(), right.toString(), bottom.toString()];
                                break;
                            }
                        }
                    }
                }

                if (!文章匹配) {
                    console.log("❌ 方法3失败，尝试方法4");

                    // 方法4：备用方法，查找包含"视频"或"笔记"的文章
                    文章匹配 = xmlContent.match(/content-desc="[^"]*视频[^"]*"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);

                    if (!文章匹配) {
                        文章匹配 = xmlContent.match(/content-desc="[^"]*笔记[^"]*"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
                    }

                    if (!文章匹配) {
                        文章匹配 = xmlContent.match(/content-desc="[^"]*来自[^"]*"[^>]*bounds="\[(\d+),(\d+)\]\[(\d+),(\d+)\]"/);
                    }

                    if (文章匹配) {
                        console.log("✅ 方法4成功 - 找到备用文章");
                    } else {
                        console.log("❌ 所有方法都无法找到合适的文章");
                    }
                }
            }
        }

        if (文章匹配) {
            let left = parseInt(文章匹配[1]);
            let top = parseInt(文章匹配[2]);
            let right = parseInt(文章匹配[3]);
            let bottom = parseInt(文章匹配[4]);

            // 提取content-desc内容用于确认
            let contentDescMatch = 文章匹配[0].match(/content-desc="([^"]*)"/);
            let contentDesc = contentDescMatch ? contentDescMatch[1] : "未知";

            console.log(`找到第一个文章区域: [${left},${top}][${right},${bottom}]`);
            console.log(`文章内容描述: ${contentDesc}`);

            // 计算点赞按钮位置（使用222.js中验证过的方法）
            let 点赞X = right - 40;  // 距离右边缘40像素（X坐标偏移）

            // 根据实际测试，Y坐标应该在1180左右
            // 如果文章底边是1242，那么1180-1242=-62，说明点赞按钮在文章区域上方62像素
            let 点赞Y = bottom - 62; // 距离底边缘-62像素（根据实际测试调整）

            // 最终坐标就是计算出的坐标
            let 最终Y = 点赞Y;

            console.log(`计算的点赞位置: (${点赞X}, ${点赞Y})`);
            console.log(`最终点击位置: (${点赞X}, ${最终Y})`);

            return {
                x: 点赞X,
                y: 最终Y,
                原始区域: {
                    left: left,
                    top: top,
                    right: right,
                    bottom: bottom
                },
                计算过程: {
                    文章底边: bottom,
                    X偏移: -40,
                    Y偏移: -62,
                    计算说明: "Y坐标=bottom-62，目标约1180",
                    最终坐标: `(${点赞X}, ${最终Y})`
                }
            };
        } else {
            console.error("未找到文章元素的bounds信息");
            return null;
        }
    } catch (e) {
        console.error("提取点赞位置出错:", e.message);
        return null;
    }
}

/**
 * 获取第一个点赞位置坐标
 * 综合方法：获取XML信息并提取第一个点赞位置
 * @returns {Object|null} 包含x,y坐标的对象，失败返回null
 */
function 获取第一个点赞位置() {
    console.log("=== 开始获取第一个点赞位置坐标 ===");

    try {
        // 1. 获取XML信息
        let xml内容 = 获取界面XML信息();
        if (!xml内容) {
            console.error("获取XML信息失败");
            return null;
        }

        // 2. 提取点赞位置
        let 点赞位置 = 提取第一个点赞位置(xml内容);
        if (!点赞位置) {
            console.error("提取点赞位置失败");
            return null;
        }

        console.log("=== 成功获取点赞位置坐标 ===");
        console.log(`最终坐标: (${点赞位置.x}, ${点赞位置.y})`);
        console.log(`原始文章区域: [${点赞位置.原始区域.left},${点赞位置.原始区域.top}][${点赞位置.原始区域.right},${点赞位置.原始区域.bottom}]`);

        return 点赞位置;

    } catch (e) {
        console.error("获取第一个点赞位置出错:", e.message);
        return null;
    }
}

/**
 * 测试获取第一个点赞位置功能
 * 用于调试和验证点赞位置提取是否正确
 */
function 测试获取点赞位置() {
    console.log("🧪 开始测试获取点赞位置功能...");

    try {
        // 获取点赞位置
        let 点赞位置 = 获取第一个点赞位置();

        if (点赞位置) {
            console.log("✅ 测试成功！");
            console.log("📍 点赞位置坐标:");
            console.log(`   X坐标: ${点赞位置.x}`);
            console.log(`   Y坐标: ${点赞位置.y}`);
            console.log("📊 详细信息:");
            console.log(`   原始文章区域: [${点赞位置.原始区域.left},${点赞位置.原始区域.top}][${点赞位置.原始区域.right},${点赞位置.原始区域.bottom}]`);
            console.log(`   计算过程: 原始Y=${点赞位置.计算过程.原始Y}, 偏移值=${点赞位置.计算过程.偏移值}, 最终Y=${点赞位置.计算过程.最终Y}`);

            // 可选：执行点击测试（注释掉避免误操作）
            // console.log("🖱️ 执行点击测试...");
            // DeviceOperation.点击(点赞位置.x, 点赞位置.y);
            // console.log("点击已执行");

            return 点赞位置;
        } else {
            console.error("❌ 测试失败：无法获取点赞位置");
            return null;
        }
    } catch (e) {
        console.error("❌ 测试出错:", e.message);
        return null;
    }
}

// 如果直接运行此文件，执行测试
if (typeof module === 'undefined' || require.main === module) {
    console.log("🚀 直接运行xiaohongshu.js，开始测试...");
    测试获取点赞位置();
}

