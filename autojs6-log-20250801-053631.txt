
05:36:03.533/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
05:36:03.533/D: 🔛🔛🔛====获取XML文字信息开始调用====🔛🔛🔛
05:36:03.533/D: 参数：必须关键词="选择*个兴趣", 匹配关键词="已有账号，去登录", 匹配数量=1, 排除关键词=""
05:36:03.533/D: 使用缓存的XML内容
05:36:06.104/D: 🔍 通配符匹配结果: false
05:36:06.105/D: 🔍 检查必须关键词通配符匹配: "选择4个兴趣" vs "选择*个兴趣"
05:36:06.105/D: 🔍 通配符匹配详情: 文字="选择4个兴趣", 模式="选择*个兴趣", 正则="选择\.*个兴趣"
05:36:06.105/D: 🔍 正则匹配结果: false
05:36:06.105/D: 🔍 通配符匹配结果: false
05:36:06.105/D: 🔍 检查必须关键词通配符匹配: "已有账号，去登录" vs "选择*个兴趣"
05:36:06.106/D: 🔍 通配符匹配详情: 文字="已有账号，去登录", 模式="选择*个兴趣", 正则="选择\.*个兴趣"
05:36:06.106/D: 🔍 正则匹配结果: false
05:36:06.106/D: 🔍 通配符匹配结果: false
05:36:06.107/D: 🔚🔚🔚====获取XML文字信息调用结束，匹配到 0 个结果====🔚🔚🔚
