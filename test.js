function 重启飞行模式() {
    console.log("开始重启飞行模式...");
    
    try {
        // 打开飞行模式
        console.log("正在打开飞行模式...");
        shell("settings put global airplane_mode_on 1", true);
        shell("am broadcast -a android.intent.action.AIRPLANE_MODE --ez state true", true);
        console.log("✅ 飞行模式已打开");
        
        // 等待5秒
        console.log("等待5秒...");
        sleep(5000);
        
        // 关闭飞行模式
        console.log("正在关闭飞行模式...");
        shell("settings put global airplane_mode_on 0", true);
        shell("am broadcast -a android.intent.action.AIRPLANE_MODE --ez state false", true);
        console.log("✅ 飞行模式已关闭");
        
        console.log("🎉 飞行模式重启完成");
        return true;
    } catch (e) {
        console.error("❌ 飞行模式重启失败: " + e.message);
        return false;
    }
}
重启飞行模式()